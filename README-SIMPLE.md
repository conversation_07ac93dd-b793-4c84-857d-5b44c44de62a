# Simple VS Code Fingerprint Isolation

A lightweight script that runs VS Code with different fingerprints on each execution - **no heavy dependencies required!**

## 🚀 Quick Start

### Prerequisites
- VS Code installed with `code` command available in PATH
- `openssl` (usually pre-installed on macOS/Linux)

### Basic Usage

```bash
# Run VS Code with a new fingerprint (current directory)
./scripts/simple-vscode-runner.sh --new-fingerprint

# Run with new fingerprint in specific workspace
./scripts/simple-vscode-runner.sh --new-fingerprint /path/to/your/project

# List available fingerprints
./scripts/simple-vscode-runner.sh --list

# Use existing fingerprint
./scripts/simple-vscode-runner.sh --fingerprint-id fp_20241201_123456

# Clean up old data
./scripts/simple-vscode-runner.sh --clean
```

### NPM Scripts (even easier!)

```bash
# Quick commands
npm run vscode              # New fingerprint, current directory
npm run vscode:new          # Same as above
npm run vscode:list         # List fingerprints
npm run vscode:clean        # Clean up old data
```

## 🔧 How It Works

### Each VS Code Run Gets:
1. **Unique fingerprint** (real Rust-generated or mock fallback)
2. **Isolated user data directory** (`/tmp/vscode-fingerprint-profiles/`)
3. **Separate extension storage** (no cross-contamination)
4. **Privacy-focused settings** (telemetry off, updates disabled)
5. **Complete isolation** from your main VS Code

### Fingerprint Generation
- **First choice**: Uses your existing Rust `privacy-fingerprint-generator` if available
- **Fallback**: Generates mock fingerprints using `openssl` (no dependencies!)
- **Privacy levels**: low, medium, high, maximum

### File Structure
```
.fingerprints/                           # Generated fingerprints
├── fp_20241201_123456.json
├── fp_20241201_124567.json
└── ...

/tmp/vscode-fingerprint-profiles/        # Isolated VS Code profiles
├── vscode-fp-fp_20241201_123456/
│   ├── user-data/
│   │   ├── User/
│   │   │   ├── settings.json
│   │   │   └── globalStorage/
│   │   │       └── fingerprint.json
│   │   └── ...
│   └── extensions/
└── ...
```

## 📋 Command Options

```bash
./scripts/simple-vscode-runner.sh [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
  --new-fingerprint, -n    Generate new fingerprint and run VS Code
  --fingerprint-id ID      Use specific fingerprint by ID
  --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: high]
  --list, -l               List available fingerprints
  --clean, -c              Clean up old profiles and fingerprints
  --help, -h               Show help
```

## 🎯 Perfect For Testing

### Extension Trial Prevention
```bash
# Test with first fingerprint
./scripts/simple-vscode-runner.sh --new-fingerprint

# Install your extension, test trial functionality

# Test with different fingerprint (simulates new device)
./scripts/simple-vscode-runner.sh --new-fingerprint --privacy-level maximum

# Check if extension detects different device
```

### Privacy Levels Testing
```bash
# Test different privacy levels
./scripts/simple-vscode-runner.sh --new-fingerprint --privacy-level low
./scripts/simple-vscode-runner.sh --new-fingerprint --privacy-level high
./scripts/simple-vscode-runner.sh --new-fingerprint --privacy-level maximum
```

### Consistent Testing
```bash
# Generate fingerprint once
./scripts/simple-vscode-runner.sh --new-fingerprint
# Note the fingerprint ID from output

# Reuse same fingerprint for consistent testing
./scripts/simple-vscode-runner.sh --fingerprint-id fp_20241201_123456
```

## 🛡️ Privacy & Security

### Isolation Features
- **Complete filesystem isolation** between VS Code instances
- **No shared state** between different fingerprint sessions
- **Separate extension storage** prevents cross-contamination
- **Privacy-focused settings** (telemetry disabled, updates off)

### Fingerprint Components
Each fingerprint includes:
- **Device ID**: Unique identifier for the session
- **Hardware signature**: CPU, memory, architecture (anonymized)
- **System signature**: OS family, version class, timezone
- **VS Code signature**: Version class, machine ID hash
- **Network signature**: MAC address signature (hashed)
- **Privacy metadata**: Anonymization level, protection settings

### Data Protection
- All sensitive data is **cryptographically hashed**
- **No personally identifiable information** stored in plain text
- **Automatic cleanup** of old profiles and fingerprints
- **Configurable privacy levels** for different use cases

## 🔧 Troubleshooting

### VS Code Command Not Found
```bash
# On macOS: Open VS Code, then:
# Command Palette (Cmd+Shift+P) → "Shell Command: Install 'code' command in PATH"

# Or add to your shell profile:
export PATH="/Applications/Visual Studio Code.app/Contents/Resources/app/bin:$PATH"
```

### Permission Errors
```bash
# Make script executable
chmod +x scripts/simple-vscode-runner.sh
```

### OpenSSL Not Found
```bash
# On macOS (usually pre-installed)
which openssl

# On Linux
sudo apt-get install openssl  # Ubuntu/Debian
sudo yum install openssl      # CentOS/RHEL
```

### Clean Up Issues
```bash
# Manual cleanup if needed
rm -rf /tmp/vscode-fingerprint-profiles/
rm -rf .fingerprints/
```

## 🎯 Advantages of This Approach

### ✅ **Lightweight**
- No heavy Nix dependencies
- No complex build processes
- Works with existing VS Code installation

### ✅ **Fast**
- Instant startup (no compilation)
- Quick fingerprint generation
- Immediate VS Code launch

### ✅ **Flexible**
- Uses Rust tools if available
- Falls back to mock generation
- Works on any Unix-like system

### ✅ **Isolated**
- Complete VS Code isolation
- No interference with main installation
- Perfect for extension testing

### ✅ **Simple**
- Single script does everything
- Clear command-line interface
- Easy to understand and modify

## 🚀 Getting Started Now

1. **Make script executable**:
   ```bash
   chmod +x scripts/simple-vscode-runner.sh
   ```

2. **Run VS Code with new fingerprint**:
   ```bash
   ./scripts/simple-vscode-runner.sh --new-fingerprint
   ```

3. **Test your extension** in the isolated environment!

That's it! No complex setup, no heavy dependencies - just VS Code with different fingerprints on each run! 🎯
