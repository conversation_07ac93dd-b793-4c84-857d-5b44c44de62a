#!/usr/bin/env bash

# Quick alias for VS Code fingerprint runner
# Usage: ./vscode-fp [workspace_path]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

if [[ $# -eq 0 ]]; then
    # No arguments - use current directory
    "$SCRIPT_DIR/scripts/simple-vscode-runner.sh" --new-fingerprint
else
    # Use provided workspace path
    "$SCRIPT_DIR/scripts/simple-vscode-runner.sh" --new-fingerprint "$1"
fi
