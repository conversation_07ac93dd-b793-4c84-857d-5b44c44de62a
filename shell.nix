# Legacy Nix shell for VS Code Extension Security Research
# For users who prefer shell.nix over flakes

{ pkgs ? import <nixpkgs> {
    overlays = [
      (import (builtins.fetchTarball "https://github.com/oxalica/rust-overlay/archive/master.tar.gz"))
    ];
  }
}:

let
  # Rust toolchain
  rustToolchain = pkgs.rust-bin.stable.latest.default.override {
    extensions = [ "rust-src" "rust-analyzer" ];
  };

  # VS Code with essential extensions
  vscodeWithExtensions = pkgs.vscode-with-extensions.override {
    vscodeExtensions = with pkgs.vscode-extensions; [
      rust-lang.rust-analyzer
      ms-vscode.vscode-typescript-next
      ms-vscode.vscode-json
      ms-vscode.vscode-eslint
      ms-python.python
    ];
  };

in pkgs.mkShell {
  buildInputs = with pkgs; [
    # Rust development
    rustToolchain
    pkg-config
    openssl
    
    # Node.js for frontend
    nodejs_20
    nodePackages.pnpm
    
    # VS Code
    vscodeWithExtensions
    
    # System tools
    jq
    curl
    git
    procps
    coreutils
  ];

  shellHook = ''
    echo "🚀 VS Code Extension Security Research Environment (Legacy)"
    echo "=========================================================="
    echo ""
    echo "This is the legacy shell.nix environment."
    echo "For full features, consider using: nix develop"
    echo ""
    echo "Available tools:"
    echo "  - Rust ${rustToolchain.version}"
    echo "  - VS Code with extensions"
    echo "  - Node.js ${pkgs.nodejs_20.version}"
    echo ""
    echo "To run VS Code with fingerprint isolation:"
    echo "  ./scripts/nix-vscode-runner.sh --new-fingerprint"
    echo ""
  '';
}
