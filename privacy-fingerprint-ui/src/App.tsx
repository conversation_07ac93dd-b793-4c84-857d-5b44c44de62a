import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';

// Components
import Layout from '@components/Layout';
import Dashboard from '@components/Dashboard';
import FingerprintGenerator from '@components/FingerprintGenerator';
import PrivacyAnalysis from '@components/PrivacyAnalysis';
import RotationManager from '@components/RotationManager';
import VSCodeIntegration from '@components/VSCodeIntegration';
import EducationalHub from '@components/EducationalHub';
import Settings from '@components/Settings';
import LoadingScreen from '@components/LoadingScreen';
import ErrorBoundary from '@components/ErrorBoundary';

// Hooks and stores
import { useAppStore } from '@stores/appStore';
import { useNotificationStore } from '@stores/notificationStore';

// Types
import type { AppConfig, SystemInfo } from '@types/index';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  
  const { 
    setConfig, 
    setSystemInfo, 
    setRustToolAvailable,
    config 
  } = useAppStore();
  
  const { addNotification } = useNotificationStore();

  // Initialize the application
  useEffect(() => {
    const initializeApp = async () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] ========== FRONTEND APP INITIALIZATION ==========`);
      console.log(`[${timestamp}] App.tsx useEffect() triggered`);

      try {
        console.log(`[${timestamp}] About to call check_rust_tool_availability`);

        // Check if Rust tool is available
        const rustToolAvailable = await invoke<boolean>('check_rust_tool_availability');

        console.log(`[${timestamp}] check_rust_tool_availability returned:`, rustToolAvailable);
        console.log(`[${timestamp}] Setting rustToolAvailable state to:`, rustToolAvailable);

        setRustToolAvailable(rustToolAvailable);

        if (!rustToolAvailable) {
          console.log(`[${timestamp}] Rust tool not available, showing notification`);
          addNotification({
            type: 'warning',
            title: 'Rust Tool Not Available',
            message: 'The privacy fingerprint generator Rust tool is not available. Some features may be limited.',
            duration: 10000,
          });
          console.log(`[${timestamp}] Notification added to queue`);
        } else {
          console.log(`[${timestamp}] Rust tool is available, no notification needed`);
        }

        console.log(`[${timestamp}] Loading app configuration...`);
        // Load app configuration
        const appConfig = await invoke<AppConfig>('get_config');
        console.log(`[${timestamp}] App config loaded:`, appConfig);
        setConfig(appConfig);

        console.log(`[${timestamp}] Getting system information...`);
        // Get system information
        const systemInfo = await invoke<SystemInfo>('get_system_info');
        console.log(`[${timestamp}] System info loaded:`, systemInfo);
        setSystemInfo(systemInfo);

        console.log(`[${timestamp}] Setting up event listeners...`);
        // Set up event listeners for Tauri events
        setupEventListeners();

        console.log(`[${timestamp}] Setting isInitialized to true`);
        setIsInitialized(true);

        console.log(`[${timestamp}] Adding success notification`);
        addNotification({
          type: 'success',
          title: 'Application Initialized',
          message: 'Privacy Fingerprint Generator is ready to use.',
          duration: 3000,
        });

        console.log(`[${timestamp}] ========== FRONTEND INITIALIZATION COMPLETE ==========`);

      } catch (error) {
        const errorTimestamp = new Date().toISOString();
        console.error(`[${errorTimestamp}] Failed to initialize app:`, error);
        console.error(`[${errorTimestamp}] Error details:`, {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });

        addNotification({
          type: 'error',
          title: 'Initialization Failed',
          message: `Failed to initialize the application: ${error}`,
          duration: 10000,
        });
      } finally {
        const finalTimestamp = new Date().toISOString();
        console.log(`[${finalTimestamp}] Setting isLoading to false`);
        setIsLoading(false);
      }
    };

    console.log(`[${new Date().toISOString()}] About to call initializeApp()`);
    initializeApp();
  }, [setConfig, setSystemInfo, setRustToolAvailable, addNotification]);

  // Set up event listeners for Tauri events
  const setupEventListeners = async () => {
    try {
      // Listen for fingerprint generation events
      await listen('fingerprint_generation_started', () => {
        addNotification({
          type: 'info',
          title: 'Generating Fingerprint',
          message: 'Privacy fingerprint generation started...',
          duration: 3000,
        });
      });

      await listen('fingerprint_generation_completed', (event) => {
        addNotification({
          type: 'success',
          title: 'Fingerprint Generated',
          message: 'Privacy fingerprint generated successfully!',
          duration: 5000,
        });
      });

      await listen('fingerprint_generation_failed', (event) => {
        addNotification({
          type: 'error',
          title: 'Generation Failed',
          message: `Failed to generate fingerprint: ${event.payload}`,
          duration: 8000,
        });
      });

      // Listen for rotation events
      await listen('fingerprint_rotation_started', () => {
        addNotification({
          type: 'info',
          title: 'Rotating Fingerprint',
          message: 'Fingerprint rotation started for privacy protection...',
          duration: 3000,
        });
      });

      await listen('fingerprint_rotation_completed', (event) => {
        addNotification({
          type: 'success',
          title: 'Fingerprint Rotated',
          message: 'Fingerprint rotated successfully for enhanced privacy!',
          duration: 5000,
        });
      });

      // Listen for privacy analysis events
      await listen('privacy_analysis_started', () => {
        addNotification({
          type: 'info',
          title: 'Analyzing Privacy',
          message: 'Privacy protection analysis started...',
          duration: 3000,
        });
      });

      await listen('privacy_analysis_completed', (event) => {
        addNotification({
          type: 'success',
          title: 'Privacy Analysis Complete',
          message: 'Privacy protection analysis completed successfully!',
          duration: 5000,
        });
      });

      // Listen for VS Code integration events
      await listen('vscode_integration_started', () => {
        addNotification({
          type: 'info',
          title: 'VS Code Integration',
          message: 'VS Code integration started...',
          duration: 3000,
        });
      });

      await listen('vscode_integration_completed', (event) => {
        addNotification({
          type: 'success',
          title: 'Integration Complete',
          message: 'VS Code integration completed successfully!',
          duration: 5000,
        });
      });

    } catch (error) {
      console.error('Failed to set up event listeners:', error);
    }
  };

  // Show loading screen while initializing
  if (isLoading) {
    return <LoadingScreen />;
  }

  // Show error if initialization failed
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-monokai-bg-primary">
        <div className="text-center">
          <div className="text-monokai-red text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">
            Initialization Failed
          </h1>
          <p className="text-monokai-text-secondary mb-4">
            The application failed to initialize properly.
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="btn btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-primary">
        <Layout>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/generate" element={<FingerprintGenerator />} />
            <Route path="/analysis" element={<PrivacyAnalysis />} />
            <Route path="/rotation" element={<RotationManager />} />
            <Route path="/integration" element={<VSCodeIntegration />} />
            <Route path="/education" element={<EducationalHub />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Layout>
      </div>
    </ErrorBoundary>
  );
}

export default App;
