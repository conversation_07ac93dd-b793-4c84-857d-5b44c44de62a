import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { 
  AppConfig, 
  SystemInfo, 
  PrivacyFingerprint, 
  FingerprintHistoryEntry,
  PrivacyAnalysis,
  IntegrationStatus,
  ProgressState,
  UIState
} from '@types/index';

interface AppStore {
  // Configuration
  config: AppConfig;
  setConfig: (config: AppConfig) => void;
  updateConfig: (updates: Partial<AppConfig>) => void;

  // System information
  systemInfo: SystemInfo | null;
  setSystemInfo: (info: SystemInfo) => void;

  // Rust tool availability
  rustToolAvailable: boolean;
  setRustToolAvailable: (available: boolean) => void;

  // Current fingerprint
  currentFingerprint: PrivacyFingerprint | null;
  setCurrentFingerprint: (fingerprint: PrivacyFingerprint | null) => void;

  // Fingerprint history
  fingerprintHistory: FingerprintHistoryEntry[];
  setFingerprintHistory: (history: FingerprintHistoryEntry[]) => void;
  addToHistory: (entry: FingerprintHistoryEntry) => void;
  clearHistory: () => void;

  // Privacy analysis
  currentAnalysis: PrivacyAnalysis | null;
  setCurrentAnalysis: (analysis: PrivacyAnalysis | null) => void;

  // VS Code integration
  integrationStatus: IntegrationStatus | null;
  setIntegrationStatus: (status: IntegrationStatus | null) => void;

  // Progress tracking
  progressState: ProgressState;
  setProgressState: (state: ProgressState) => void;
  updateProgress: (progress: number, message?: string, stage?: string) => void;
  resetProgress: () => void;

  // UI state
  uiState: UIState;
  setUIState: (state: UIState) => void;
  updateUIState: (updates: Partial<UIState>) => void;
  toggleSidebar: () => void;

  // Loading states
  isGenerating: boolean;
  setIsGenerating: (loading: boolean) => void;
  isRotating: boolean;
  setIsRotating: (loading: boolean) => void;
  isAnalyzing: boolean;
  setIsAnalyzing: (loading: boolean) => void;
  isIntegrating: boolean;
  setIsIntegrating: (loading: boolean) => void;

  // Error handling
  lastError: string | null;
  setLastError: (error: string | null) => void;
  clearError: () => void;

  // Actions
  reset: () => void;
}

const defaultConfig: AppConfig = {
  privacy_level: 'High',
  auto_rotation: true,
  rotation_interval: 24,
  extension_path: undefined,
  theme: 'monokai-pro',
};

const defaultUIState: UIState = {
  sidebarCollapsed: false,
  currentView: 'dashboard',
  isLoading: false,
  theme: 'monokai-pro',
};

const defaultProgressState: ProgressState = {
  isActive: false,
  progress: 0,
  message: '',
  stage: '',
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Configuration
      config: defaultConfig,
      setConfig: (config) => set({ config }),
      updateConfig: (updates) => set((state) => ({ 
        config: { ...state.config, ...updates } 
      })),

      // System information
      systemInfo: null,
      setSystemInfo: (systemInfo) => set({ systemInfo }),

      // Rust tool availability
      rustToolAvailable: false,
      setRustToolAvailable: (rustToolAvailable) => {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ========== RUST TOOL AVAILABILITY STATE CHANGE ==========`);
        console.log(`[${timestamp}] setRustToolAvailable() called with:`, rustToolAvailable);
        console.log(`[${timestamp}] Previous value:`, get().rustToolAvailable);
        console.log(`[${timestamp}] Stack trace:`, new Error().stack);
        set({ rustToolAvailable });
        console.log(`[${timestamp}] State updated, new value:`, get().rustToolAvailable);
      },

      // Current fingerprint
      currentFingerprint: null,
      setCurrentFingerprint: (currentFingerprint) => set({ currentFingerprint }),

      // Fingerprint history
      fingerprintHistory: [],
      setFingerprintHistory: (fingerprintHistory) => set({ fingerprintHistory }),
      addToHistory: (entry) => set((state) => ({
        fingerprintHistory: [entry, ...state.fingerprintHistory].slice(0, 100) // Keep last 100 entries
      })),
      clearHistory: () => set({ fingerprintHistory: [] }),

      // Privacy analysis
      currentAnalysis: null,
      setCurrentAnalysis: (currentAnalysis) => set({ currentAnalysis }),

      // VS Code integration
      integrationStatus: null,
      setIntegrationStatus: (integrationStatus) => set({ integrationStatus }),

      // Progress tracking
      progressState: defaultProgressState,
      setProgressState: (progressState) => set({ progressState }),
      updateProgress: (progress, message, stage) => set((state) => ({
        progressState: {
          ...state.progressState,
          progress,
          message: message || state.progressState.message,
          stage: stage || state.progressState.stage,
          isActive: true,
        }
      })),
      resetProgress: () => set({ progressState: defaultProgressState }),

      // UI state
      uiState: defaultUIState,
      setUIState: (uiState) => set({ uiState }),
      updateUIState: (updates) => set((state) => ({
        uiState: { ...state.uiState, ...updates }
      })),
      toggleSidebar: () => set((state) => ({
        uiState: { 
          ...state.uiState, 
          sidebarCollapsed: !state.uiState.sidebarCollapsed 
        }
      })),

      // Loading states
      isGenerating: false,
      setIsGenerating: (isGenerating) => set({ isGenerating }),
      isRotating: false,
      setIsRotating: (isRotating) => set({ isRotating }),
      isAnalyzing: false,
      setIsAnalyzing: (isAnalyzing) => set({ isAnalyzing }),
      isIntegrating: false,
      setIsIntegrating: (isIntegrating) => set({ isIntegrating }),

      // Error handling
      lastError: null,
      setLastError: (lastError) => set({ lastError }),
      clearError: () => set({ lastError: null }),

      // Actions
      reset: () => set({
        config: defaultConfig,
        currentFingerprint: null,
        fingerprintHistory: [],
        currentAnalysis: null,
        integrationStatus: null,
        progressState: defaultProgressState,
        uiState: defaultUIState,
        isGenerating: false,
        isRotating: false,
        isAnalyzing: false,
        isIntegrating: false,
        lastError: null,
      }),
    }),
    {
      name: 'privacy-fingerprint-app-store',
      partialize: (state) => ({
        config: state.config,
        fingerprintHistory: state.fingerprintHistory,
        uiState: state.uiState,
        // Don't persist sensitive data like current fingerprint
      }),
    }
  )
);

// Selectors for computed values
export const useAppSelectors = () => {
  const store = useAppStore();
  
  return {
    // Check if app is ready
    isAppReady: store.systemInfo !== null && store.config !== null,
    
    // Check if any operation is in progress
    isAnyOperationInProgress: store.isGenerating || store.isRotating || store.isAnalyzing || store.isIntegrating,
    
    // Get privacy level color
    getPrivacyLevelColor: (level: string) => {
      switch (level) {
        case 'Low': return '#FF6188';
        case 'Medium': return '#FFD866';
        case 'High': return '#A9DC76';
        case 'Maximum': return '#AB9DF2';
        default: return '#FCFCFA';
      }
    },
    
    // Get privacy score color
    getPrivacyScoreColor: (score: number) => {
      if (score >= 0.8) return '#A9DC76';
      if (score >= 0.6) return '#FFD866';
      if (score >= 0.4) return '#FC9867';
      return '#FF6188';
    },
    
    // Check if fingerprint needs rotation
    needsRotation: () => {
      const { currentFingerprint } = store;
      if (!currentFingerprint || !currentFingerprint.rotation_enabled) return false;
      
      if (currentFingerprint.next_rotation) {
        const nextRotation = new Date(currentFingerprint.next_rotation);
        return Date.now() >= nextRotation.getTime();
      }
      
      return false;
    },
    
    // Get latest history entry
    getLatestHistoryEntry: () => {
      return store.fingerprintHistory[0] || null;
    },
    
    // Get history by action type
    getHistoryByAction: (action: string) => {
      return store.fingerprintHistory.filter(entry => entry.action === action);
    },
    
    // Calculate average privacy score from history
    getAveragePrivacyScore: () => {
      const { fingerprintHistory } = store;
      if (fingerprintHistory.length === 0) return 0;
      
      const sum = fingerprintHistory.reduce((acc, entry) => acc + entry.privacy_score, 0);
      return sum / fingerprintHistory.length;
    },
  };
};
