import { create } from 'zustand';
import type { Notification } from '@types/index';

interface NotificationStore {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  markAsRead: (id: string) => void;
  getUnreadCount: () => number;
}

let notificationId = 0;

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],

  addNotification: (notification) => {
    const timestamp = new Date().toISOString();
    const id = `notification-${++notificationId}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
    };

    console.log(`[${timestamp}] ========== NOTIFICATION ADDED ==========`);
    console.log(`[${timestamp}] Notification details:`, {
      id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      duration: notification.duration
    });
    console.log(`[${timestamp}] Stack trace:`, new Error().stack);

    set((state) => ({
      notifications: [newNotification, ...state.notifications].slice(0, 50), // Keep last 50 notifications
    }));

    console.log(`[${timestamp}] Notification added to store, total notifications:`, get().notifications.length);

    // Auto-remove notification after duration
    if (notification.duration && notification.duration > 0) {
      console.log(`[${timestamp}] Setting auto-remove timer for ${notification.duration}ms`);
      setTimeout(() => {
        console.log(`[${new Date().toISOString()}] Auto-removing notification ${id}`);
        get().removeNotification(id);
      }, notification.duration);
    }
  },

  removeNotification: (id) => {
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    }));
  },

  clearNotifications: () => {
    set({ notifications: [] });
  },

  markAsRead: (id) => {
    set((state) => ({
      notifications: state.notifications.map((n) =>
        n.id === id ? { ...n, read: true } : n
      ),
    }));
  },

  getUnreadCount: () => {
    return get().notifications.filter((n) => !n.read).length;
  },
}));

// Notification helper functions
export const notificationHelpers = {
  success: (title: string, message: string, duration = 5000) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] notificationHelpers.success() called:`, { title, message, duration });
    useNotificationStore.getState().addNotification({
      type: 'success',
      title,
      message,
      duration,
    });
  },

  error: (title: string, message: string, duration = 8000) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] notificationHelpers.error() called:`, { title, message, duration });
    useNotificationStore.getState().addNotification({
      type: 'error',
      title,
      message,
      duration,
    });
  },

  warning: (title: string, message: string, duration = 6000) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] notificationHelpers.warning() called:`, { title, message, duration });
    console.log(`[${timestamp}] Warning notification stack trace:`, new Error().stack);
    useNotificationStore.getState().addNotification({
      type: 'warning',
      title,
      message,
      duration,
    });
  },

  info: (title: string, message: string, duration = 4000) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] notificationHelpers.info() called:`, { title, message, duration });
    useNotificationStore.getState().addNotification({
      type: 'info',
      title,
      message,
      duration,
    });
  },
};
