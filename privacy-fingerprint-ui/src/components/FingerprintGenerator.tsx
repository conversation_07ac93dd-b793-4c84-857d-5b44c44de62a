import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Fingerprint, 
  Shield, 
  Settings, 
  Play, 
  Download, 
  Copy,
  Eye,
  EyeOff,
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useAppStore } from '@stores/appStore';
import { notificationHelpers } from '@stores/notificationStore';
import JsonViewer from './JsonViewer';
import type { GenerateFingerprintRequest, GenerateFingerprintResponse, PrivacyLevel } from '@types/index';

const FingerprintGenerator: React.FC = () => {
  const [privacyLevel, setPrivacyLevel] = useState<PrivacyLevel>('High');
  const [realSystem, setRealSystem] = useState(false); // Default to mock mode since Rust tool has issues
  const [count, setCount] = useState(1);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showResult, setShowResult] = useState(false);
  
  const { 
    isGenerating, 
    setIsGenerating, 
    currentFingerprint, 
    setCurrentFingerprint,
    addToHistory,
    rustToolAvailable 
  } = useAppStore();

  const handleGenerate = async () => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ========== FINGERPRINT GENERATION STARTED ==========`);
    console.log(`[${timestamp}] FingerprintGenerator.handleGenerate() called`);
    console.log(`[${timestamp}] Current state:`, {
      rustToolAvailable,
      realSystem,
      privacyLevel,
      count
    });

    if (!rustToolAvailable && realSystem) {
      console.log(`[${timestamp}] Rust tool not available but realSystem=true, silently switching to mock mode`);
      // Silently switch to mock mode without showing notification
      // since we know the tool has stack overflow issues
      setRealSystem(false);
    }

    console.log(`[${timestamp}] Setting isGenerating to true`);
    setIsGenerating(true);
    setShowResult(false);

    try {
      const request: GenerateFingerprintRequest = {
        privacy_level: privacyLevel,
        real_system: realSystem,
        count,
      };

      const response = await invoke<GenerateFingerprintResponse>('generate_fingerprint', { request });

      if (response.success && response.fingerprint) {
        setCurrentFingerprint(response.fingerprint);
        addToHistory({
          id: response.fingerprint.id,
          timestamp: response.fingerprint.created_at,
          privacy_level: privacyLevel,
          privacy_score: response.fingerprint.privacy_metadata.privacy_score,
          action: 'generated',
        });
        setShowResult(true);
        
        notificationHelpers.success(
          'Fingerprint Generated',
          `Successfully generated ${privacyLevel} privacy level fingerprint.`
        );
      } else {
        throw new Error(response.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Failed to generate fingerprint:', error);
      notificationHelpers.error(
        'Generation Failed',
        `Failed to generate fingerprint: ${error}`
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyFingerprint = async () => {
    if (currentFingerprint) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(currentFingerprint, null, 2));
        notificationHelpers.success('Copied', 'Fingerprint copied to clipboard');
      } catch (error) {
        notificationHelpers.error('Copy Failed', 'Failed to copy fingerprint to clipboard');
      }
    }
  };

  const handleDownloadFingerprint = async () => {
    if (currentFingerprint) {
      try {
        await invoke('save_fingerprint_to_file', {
          filePath: `fingerprint_${currentFingerprint.id}.json`
        });
        notificationHelpers.success('Downloaded', 'Fingerprint saved to file');
      } catch (error) {
        notificationHelpers.error('Download Failed', 'Failed to save fingerprint to file');
      }
    }
  };

  const getPrivacyLevelInfo = (level: PrivacyLevel) => {
    switch (level) {
      case 'Low':
        return {
          description: 'Basic privacy protection with minimal anonymization',
          score: '30-50%',
          color: 'text-monokai-red',
          features: ['Basic hashing', 'Minimal obfuscation', 'Standard data collection']
        };
      case 'Medium':
        return {
          description: 'Moderate privacy protection with enhanced anonymization',
          score: '50-70%',
          color: 'text-monokai-yellow',
          features: ['Enhanced hashing', 'Data classification', 'Reduced data collection']
        };
      case 'High':
        return {
          description: 'Strong privacy protection with comprehensive anonymization',
          score: '70-90%',
          color: 'text-monokai-green',
          features: ['Strong anonymization', 'Data obfuscation', 'Minimal data collection']
        };
      case 'Maximum':
        return {
          description: 'Maximum privacy protection with differential privacy',
          score: '90-95%',
          color: 'text-monokai-purple',
          features: ['Differential privacy', 'Maximum obfuscation', 'Data minimization']
        };
    }
  };

  const privacyInfo = getPrivacyLevelInfo(privacyLevel);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-monokai-text-primary mb-2">
            Generate Privacy Fingerprint
          </h1>
          <p className="text-monokai-text-secondary">
            Create a privacy-protected device fingerprint for security research and education.
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${rustToolAvailable ? 'bg-monokai-green' : 'bg-monokai-red'}`}></div>
          <span className="text-sm text-monokai-text-muted">
            {rustToolAvailable ? 'Rust Tool Available' : 'Limited Mode'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center space-x-2">
                <Settings className="w-5 h-5 text-monokai-blue" />
                <h3 className="font-semibold text-monokai-text-primary">Configuration</h3>
              </div>
            </div>
            
            <div className="card-body space-y-6">
              {/* Privacy Level */}
              <div>
                <label className="block text-sm font-medium text-monokai-text-primary mb-3">
                  Privacy Level
                </label>
                <div className="space-y-2">
                  {(['Low', 'Medium', 'High', 'Maximum'] as PrivacyLevel[]).map((level) => {
                    const info = getPrivacyLevelInfo(level);
                    return (
                      <label
                        key={level}
                        className={`
                          flex items-center p-3 rounded-lg border cursor-pointer transition-all
                          ${privacyLevel === level 
                            ? 'border-monokai-green bg-monokai-green bg-opacity-10' 
                            : 'border-monokai-bg-tertiary hover:border-monokai-bg-hover'
                          }
                        `}
                      >
                        <input
                          type="radio"
                          name="privacyLevel"
                          value={level}
                          checked={privacyLevel === level}
                          onChange={(e) => setPrivacyLevel(e.target.value as PrivacyLevel)}
                          className="sr-only"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium text-monokai-text-primary">{level}</span>
                            <span className={`text-sm font-medium ${info.color}`}>
                              {info.score}
                            </span>
                          </div>
                          <p className="text-xs text-monokai-text-muted">
                            {info.description}
                          </p>
                        </div>
                      </label>
                    );
                  })}
                </div>
                
                {/* Privacy level details */}
                <div className="mt-4 p-3 bg-monokai-bg-primary rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Info className="w-4 h-4 text-monokai-blue" />
                    <span className="text-sm font-medium text-monokai-text-primary">
                      {privacyLevel} Level Features
                    </span>
                  </div>
                  <ul className="text-xs text-monokai-text-muted space-y-1">
                    {privacyInfo.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-1 h-1 bg-monokai-green rounded-full"></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Data Source */}
              <div>
                <label className="block text-sm font-medium text-monokai-text-primary mb-3">
                  Data Source
                </label>
                <div className="space-y-2">
                  <label className={`
                    flex items-center p-3 rounded-lg border cursor-pointer transition-all
                    ${realSystem 
                      ? 'border-monokai-green bg-monokai-green bg-opacity-10' 
                      : 'border-monokai-bg-tertiary hover:border-monokai-bg-hover'
                    }
                  `}>
                    <input
                      type="radio"
                      name="dataSource"
                      checked={realSystem}
                      onChange={() => setRealSystem(true)}
                      className="sr-only"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Shield className="w-4 h-4 text-monokai-green" />
                        <span className="font-medium text-monokai-text-primary">Real System</span>
                        {!rustToolAvailable && (
                          <AlertTriangle className="w-4 h-4 text-monokai-yellow" />
                        )}
                      </div>
                      <p className="text-xs text-monokai-text-muted">
                        Use actual system information with privacy protection
                      </p>
                    </div>
                  </label>
                  
                  <label className={`
                    flex items-center p-3 rounded-lg border cursor-pointer transition-all
                    ${!realSystem 
                      ? 'border-monokai-green bg-monokai-green bg-opacity-10' 
                      : 'border-monokai-bg-tertiary hover:border-monokai-bg-hover'
                    }
                  `}>
                    <input
                      type="radio"
                      name="dataSource"
                      checked={!realSystem}
                      onChange={() => setRealSystem(false)}
                      className="sr-only"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Fingerprint className="w-4 h-4 text-monokai-blue" />
                        <span className="font-medium text-monokai-text-primary">Mock Data</span>
                      </div>
                      <p className="text-xs text-monokai-text-muted">
                        Use simulated data for testing and demonstration
                      </p>
                    </div>
                  </label>
                </div>
              </div>

              {/* Advanced Options */}
              <div>
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center space-x-2 text-sm text-monokai-blue hover:text-monokai-green transition-colors"
                >
                  {showAdvanced ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Options</span>
                </button>
                
                {showAdvanced && (
                  <div className="mt-3 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-monokai-text-primary mb-2">
                        Count
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={count}
                        onChange={(e) => setCount(parseInt(e.target.value) || 1)}
                        className="w-full"
                      />
                      <p className="text-xs text-monokai-text-muted mt-1">
                        Number of fingerprints to generate (1-10)
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="w-full btn btn-primary flex items-center justify-center space-x-2"
              >
                {isGenerating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    <span>Generate Fingerprint</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Fingerprint className="w-5 h-5 text-monokai-green" />
                  <h3 className="font-semibold text-monokai-text-primary">Generated Fingerprint</h3>
                </div>
                
                {currentFingerprint && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleCopyFingerprint}
                      className="btn btn-ghost btn-sm flex items-center space-x-1"
                      title="Copy to clipboard"
                    >
                      <Copy className="w-4 h-4" />
                      <span>Copy</span>
                    </button>
                    <button
                      onClick={handleDownloadFingerprint}
                      className="btn btn-ghost btn-sm flex items-center space-x-1"
                      title="Download as file"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            <div className="card-body">
              {!currentFingerprint ? (
                <div className="text-center py-12">
                  <Fingerprint className="w-16 h-16 text-monokai-text-muted mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-monokai-text-primary mb-2">
                    No Fingerprint Generated
                  </h3>
                  <p className="text-monokai-text-muted mb-6">
                    Configure your privacy settings and generate your first fingerprint.
                  </p>
                  <button
                    onClick={handleGenerate}
                    disabled={isGenerating}
                    className="btn btn-primary"
                  >
                    Generate Now
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Fingerprint summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-monokai-bg-primary rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-monokai-green mb-1">
                        {Math.round(currentFingerprint.privacy_metadata.privacy_score * 100)}%
                      </div>
                      <div className="text-sm text-monokai-text-muted">Privacy Score</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-monokai-blue mb-1">
                        {currentFingerprint.privacy_level}
                      </div>
                      <div className="text-sm text-monokai-text-muted">Privacy Level</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-monokai-purple mb-1">
                        {currentFingerprint.rotation_enabled ? 'Yes' : 'No'}
                      </div>
                      <div className="text-sm text-monokai-text-muted">Rotation Enabled</div>
                    </div>
                  </div>

                  {/* JSON viewer */}
                  <JsonViewer data={currentFingerprint} />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FingerprintGenerator;
