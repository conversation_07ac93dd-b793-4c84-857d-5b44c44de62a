// Application configuration types
export interface AppConfig {
  privacy_level: string;
  auto_rotation: boolean;
  rotation_interval: number;
  extension_path?: string;
  theme: string;
}

// System information types
export interface SystemInfo {
  os: string;
  os_version: string;
  arch: string;
  platform: string;
  hostname: string;
  username: string;
  cpu_count: number;
  cpu_brand: string;
  cpu_frequency: number;
  memory_total: number;
  memory_available: number;
  disk_total: number;
  disk_available: number;
  uptime: number;
  boot_time: number;
  rust_tool_available: boolean;
}

// Privacy levels
export type PrivacyLevel = 'Low' | 'Medium' | 'High' | 'Maximum';

// Fingerprint types
export interface PrivacyFingerprint {
  id: string;
  version: string;
  created_at: string;
  privacy_level: PrivacyLevel;
  rotation_enabled: boolean;
  next_rotation?: string;
  device_id: string;
  hardware_signature: HardwareSignature;
  system_signature: SystemSignature;
  vscode_signature: VSCodeSignature;
  network_signature: NetworkSignature;
  privacy_metadata: PrivacyMetadata;
  fingerprint_hash: string;
}

export interface HardwareSignature {
  cpu_signature: string;
  memory_class: string;
  architecture: string;
  hardware_uuid_hash: string;
  performance_class: string;
}

export interface SystemSignature {
  os_family: string;
  os_version_class: string;
  locale_region: string;
  timezone_offset: number;
  hostname_hash: string;
  username_hash: string;
}

export interface VSCodeSignature {
  version_class: string;
  machine_id_hash: string;
  session_signature: string;
  workspace_signature?: string;
  extensions_signature: string;
}

export interface NetworkSignature {
  interface_count: number;
  mac_signature: string;
  network_class: string;
  connectivity_signature: string;
}

export interface PrivacyMetadata {
  anonymization_applied: string[];
  obfuscation_methods: string[];
  data_retention_policy: string;
  privacy_score: number;
  tracking_resistance: number;
}

// Fingerprint history
export interface FingerprintHistoryEntry {
  id: string;
  timestamp: string;
  privacy_level: string;
  privacy_score: number;
  action: string;
}

// Generation and rotation types
export interface GenerateFingerprintRequest {
  privacy_level: string;
  real_system: boolean;
  count: number;
}

export interface GenerateFingerprintResponse {
  success: boolean;
  fingerprint?: PrivacyFingerprint;
  error?: string;
}

export interface FingerprintRotationResult {
  success: boolean;
  old_fingerprint_id: string;
  new_fingerprint_id: string;
  rotation_timestamp: string;
  rotation_reason: string;
  changes_applied: string[];
  similarity_score: number;
  next_rotation_due?: string;
}

export interface RotateFingerprintResponse {
  success: boolean;
  rotation_result?: FingerprintRotationResult;
  error?: string;
}

// Privacy analysis types
export interface PrivacyAnalysis {
  overall_privacy_score: number;
  anonymization_effectiveness: number;
  tracking_resistance: number;
  data_minimization_score: number;
  privacy_risks: PrivacyRisk[];
  recommendations: string[];
  compliance_status: ComplianceStatus;
}

export interface PrivacyRisk {
  risk_type: string;
  severity: RiskSeverity;
  description: string;
  mitigation: string;
}

export type RiskSeverity = 'Low' | 'Medium' | 'High' | 'Critical';

export interface ComplianceStatus {
  gdpr_compliant: boolean;
  ccpa_compliant: boolean;
  data_retention_compliant: boolean;
  anonymization_compliant: boolean;
}

// Validation types
export interface ValidationResult {
  status: string;
  privacy_score: number;
  uniqueness_score?: number;
  compliance_status?: ComplianceStatus;
  recommendations?: string[];
}

// VS Code integration types
export interface VSCodeIntegrationResult {
  success: boolean;
  extension_path: string;
  fingerprint_location: string;
  api_bindings_created: string[];
  trial_prevention_enabled: boolean;
  privacy_features_enabled: string[];
  integration_metadata: IntegrationMetadata;
}

export interface IntegrationMetadata {
  integration_timestamp: string;
  vscode_version_detected?: string;
  extension_manifest_found: boolean;
  storage_permissions: string[];
  privacy_compliance_level: string;
}

export interface IntegrationStatus {
  is_integrated: boolean;
  extension_path?: string;
  last_integration?: string;
  bindings_status: Record<string, boolean>;
  trial_prevention_active: boolean;
}

// Educational content types
export interface EducationalContent {
  topic: string;
  title: string;
  description: string;
  examples: string[];
  best_practices: string[];
  related_topics: string[];
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

// UI state types
export interface UIState {
  sidebarCollapsed: boolean;
  currentView: string;
  isLoading: boolean;
  theme: 'monokai-pro' | 'light' | 'dark';
}

// Chart data types for privacy analysis
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
}

// File operation types
export interface FileOperationResult {
  success: boolean;
  path?: string;
  error?: string;
}

// Progress tracking
export interface ProgressState {
  isActive: boolean;
  progress: number;
  message: string;
  stage: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
}

// Settings types
export interface AppSettings {
  general: GeneralSettings;
  privacy: PrivacySettings;
  integration: IntegrationSettings;
  ui: UISettings;
}

export interface GeneralSettings {
  auto_save: boolean;
  backup_enabled: boolean;
  backup_interval: number;
  max_history_entries: number;
}

export interface PrivacySettings {
  default_privacy_level: PrivacyLevel;
  auto_rotation: boolean;
  rotation_interval: number;
  anonymization_level: number;
  tracking_protection: boolean;
}

export interface IntegrationSettings {
  auto_detect_vscode: boolean;
  trial_prevention_enabled: boolean;
  binding_generation: boolean;
  integration_notifications: boolean;
}

export interface UISettings {
  theme: string;
  sidebar_collapsed: boolean;
  show_tooltips: boolean;
  animation_enabled: boolean;
  compact_mode: boolean;
}

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Event types for Tauri events
export interface TauriEvent<T = any> {
  event: string;
  payload: T;
  id: number;
  windowLabel: string;
}

// Keyboard shortcut types
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: string;
  description: string;
}

// Theme types
export interface Theme {
  name: string;
  colors: ThemeColors;
  fonts: ThemeFonts;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ThemeFonts {
  sans: string;
  mono: string;
  sizes: Record<string, string>;
  weights: Record<string, number>;
}
