use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrivacyFingerprint {
    pub id: String,
    pub version: String,
    pub created_at: String,
    pub privacy_level: PrivacyLevel,
    pub rotation_enabled: bool,
    pub next_rotation: Option<String>,
    pub device_id: String,
    pub hardware_signature: HardwareSignature,
    pub system_signature: SystemSignature,
    pub vscode_signature: VSCodeSignature,
    pub network_signature: NetworkSignature,
    pub privacy_metadata: PrivacyMetadata,
    pub fingerprint_hash: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PrivacyLevel {
    Low,
    Medium,
    High,
    Maximum,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HardwareSignature {
    pub cpu_signature: String,
    pub memory_class: String,
    pub architecture: String,
    pub hardware_uuid_hash: String,
    pub performance_class: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SystemSignature {
    pub os_family: String,
    pub os_version_class: String,
    pub locale_region: String,
    pub timezone_offset: i32,
    pub hostname_hash: String,
    pub username_hash: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VSCodeSignature {
    pub version_class: String,
    pub machine_id_hash: String,
    pub session_signature: String,
    pub workspace_signature: Option<String>,
    pub extensions_signature: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkSignature {
    pub interface_count: u32,
    pub mac_signature: String,
    pub network_class: String,
    pub connectivity_signature: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyMetadata {
    pub anonymization_applied: Vec<String>,
    pub obfuscation_methods: Vec<String>,
    pub data_retention_policy: String,
    pub privacy_score: f64,
    pub tracking_resistance: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FingerprintRotationResult {
    pub success: bool,
    pub old_fingerprint_id: String,
    pub new_fingerprint_id: String,
    pub rotation_timestamp: String,
    pub rotation_reason: String,
    pub changes_applied: Vec<String>,
    pub similarity_score: f64,
    pub next_rotation_due: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub status: String,
    pub privacy_score: f64,
    pub uniqueness_score: Option<f64>,
    pub compliance_status: Option<ComplianceStatus>,
    pub recommendations: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStatus {
    pub gdpr_compliant: bool,
    pub ccpa_compliant: bool,
    pub data_retention_compliant: bool,
    pub anonymization_compliant: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub os: String,
    pub os_version: String,
    pub arch: String,
    pub platform: String,
    pub hostname: String,
    pub username: String,
    pub cpu_count: u32,
    pub cpu_brand: String,
    pub cpu_frequency: u64,
    pub memory_total: u64,
    pub memory_available: u64,
    pub disk_total: u64,
    pub disk_available: u64,
    pub uptime: u64,
    pub boot_time: u64,
    pub rust_tool_available: bool,
}

// File operations
use tauri::{State, Window};
use crate::AppState;

#[tauri::command]
pub async fn save_fingerprint_to_file(
    file_path: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if let Some(fingerprint) = current_fp {
        let fp_json = serde_json::to_string_pretty(&fingerprint).map_err(|e| e.to_string())?;
        std::fs::write(&file_path, fp_json).map_err(|e| e.to_string())?;
        Ok(true)
    } else {
        Err("No fingerprint to save".to_string())
    }
}

#[tauri::command]
pub async fn load_fingerprint_from_file(
    file_path: String,
    state: State<'_, AppState>,
) -> Result<PrivacyFingerprint, String> {
    let fp_json = std::fs::read_to_string(&file_path).map_err(|e| e.to_string())?;
    let fingerprint: PrivacyFingerprint = serde_json::from_str(&fp_json).map_err(|e| e.to_string())?;
    
    // Update app state
    {
        let mut current_fp = state.current_fingerprint.lock().await;
        *current_fp = Some(fingerprint.clone());
    }
    
    Ok(fingerprint)
}

#[tauri::command]
pub async fn get_system_info() -> Result<SystemInfo, String> {
    use sysinfo::System;

    let mut sys = System::new_all();
    sys.refresh_all();

    // Basic system info
    let hostname = hostname::get()
        .map(|h| h.to_string_lossy().to_string())
        .unwrap_or_else(|_| "unknown".to_string());

    let username = whoami::username();
    let cpu_count = num_cpus::get() as u32;

    // OS information
    let os = std::env::consts::OS.to_string();
    let os_version = format!("{} {}", std::env::consts::OS, std::env::consts::ARCH);
    let arch = std::env::consts::ARCH.to_string();
    let platform = std::env::consts::FAMILY.to_string();

    // CPU information
    let cpu_brand = sys.global_cpu_info().brand().to_string();
    let cpu_frequency = sys.global_cpu_info().frequency();

    // Memory information (in bytes)
    let memory_total = sys.total_memory();
    let memory_available = sys.available_memory();

    // Simplified disk information - use placeholder values for now
    let disk_total = 500_000_000_000u64; // 500GB placeholder
    let disk_available = 250_000_000_000u64; // 250GB placeholder

    // System uptime - use placeholder
    let uptime = 86400u64; // 1 day placeholder
    let boot_time = chrono::Utc::now().timestamp() as u64 - uptime;

    // Check if Rust tool is available - DISABLED to prevent duplicate checks and stack overflow
    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
    log::info!("[{}] ========== RUST TOOL CHECK IN FINGERPRINT.RS - SKIPPED ==========", timestamp);
    log::info!("[{}] Skipping tool check to prevent duplicate calls and stack overflow", timestamp);

    // Always return false since we know the tool has stack overflow issues
    let rust_tool_available = false;

    log::info!("[{}] Rust tool availability set to: {}", timestamp, rust_tool_available);
    log::info!("[{}] ========== RUST TOOL CHECK COMPLETE ==========", timestamp);

    Ok(SystemInfo {
        os,
        os_version,
        arch,
        platform,
        hostname,
        username,
        cpu_count,
        cpu_brand,
        cpu_frequency,
        memory_total,
        memory_available,
        disk_total,
        disk_available,
        uptime,
        boot_time,
        rust_tool_available,
    })
}
