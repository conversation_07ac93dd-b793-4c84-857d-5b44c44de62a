// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::process::Command;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use tokio::sync::Mutex;
use chrono;

mod commands;
mod fingerprint;
mod privacy;
mod utils;

use commands::*;
use fingerprint::*;
use privacy::*;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub privacy_level: String,
    pub auto_rotation: bool,
    pub rotation_interval: u64,
    pub extension_path: Option<String>,
    pub theme: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            privacy_level: "High".to_string(),
            auto_rotation: true,
            rotation_interval: 24,
            extension_path: None,
            theme: "monokai-pro".to_string(),
        }
    }
}

#[derive(Debug, Default)]
pub struct AppState {
    pub config: Mutex<AppConfig>,
    pub current_fingerprint: Mutex<Option<PrivacyFingerprint>>,
    pub fingerprint_history: Mutex<Vec<FingerprintHistoryEntry>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FingerprintHistoryEntry {
    pub id: String,
    pub timestamp: String,
    pub privacy_level: String,
    pub privacy_score: f64,
    pub action: String, // "generated", "rotated", "analyzed"
}

fn main() {
    env_logger::init();

    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
    log::info!("[{}] ========== TAURI APP STARTUP SEQUENCE ==========", timestamp);
    log::info!("[{}] Initializing Tauri application", timestamp);

    tauri::Builder::default()
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            // Fingerprint commands
            generate_fingerprint,
            rotate_fingerprint,
            validate_fingerprint,
            get_fingerprint_history,

            // Privacy analysis commands
            analyze_privacy,
            get_privacy_recommendations,

            // VS Code integration commands
            integrate_vscode,
            get_integration_status,

            // Configuration commands
            get_config,
            update_config,
            reset_config,

            // File operations
            save_fingerprint_to_file,
            load_fingerprint_from_file,
            export_privacy_report,

            // Educational commands
            get_educational_content,
            get_privacy_explanation,

            // System commands
            get_system_info,
            check_rust_tool_availability,
        ])
        .setup(|app| {
            let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
            log::info!("[{}] Tauri setup() function called", timestamp);

            // Initialize app data directory
            let app_handle = app.handle();
            let app_dir = app_handle.path_resolver()
                .app_data_dir()
                .expect("Failed to get app data directory");

            if !app_dir.exists() {
                log::info!("[{}] Creating app data directory: {:?}", timestamp, app_dir);
                std::fs::create_dir_all(&app_dir)
                    .expect("Failed to create app data directory");
            } else {
                log::info!("[{}] App data directory already exists: {:?}", timestamp, app_dir);
            }

            log::info!("[{}] Privacy Fingerprint Generator UI started", timestamp);
            log::info!("[{}] App data directory: {:?}", timestamp, app_dir);
            log::info!("[{}] Tauri setup() completed successfully", timestamp);
            log::info!("[{}] ========== TAURI SETUP COMPLETE ==========", timestamp);

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// Helper function to execute the Rust privacy fingerprint generator
async fn execute_privacy_tool(args: Vec<&str>) -> Result<String> {
    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
    log::info!("[{}] ========== EXECUTE_PRIVACY_TOOL CALLED ==========", timestamp);
    log::info!("[{}] execute_privacy_tool() called with args: {:?}", timestamp, args);

    // Get the workspace root directory (go up from src-tauri to the main workspace)
    let current_dir = std::env::current_dir()?;
    log::info!("[{}] Current directory: {:?}", timestamp, current_dir);

    // Try multiple possible workspace root locations
    let possible_roots = vec![
        current_dir.parent(), // Go up from src-tauri
        current_dir.parent().and_then(|p| p.parent()), // Go up from privacy-fingerprint-ui/src-tauri
        Some(current_dir.as_path()), // Current directory
    ];

    let mut workspace_root = None;
    for root in possible_roots {
        if let Some(root_path) = root {
            let cargo_toml = root_path.join("Cargo.toml");
            if cargo_toml.exists() {
                // Check if this Cargo.toml contains the privacy-fingerprint-generator package
                if let Ok(content) = std::fs::read_to_string(&cargo_toml) {
                    if content.contains("privacy-fingerprint-generator") {
                        workspace_root = Some(root_path);
                        break;
                    }
                }
            }
        }
    }

    let workspace_root = workspace_root
        .ok_or_else(|| anyhow::anyhow!("Could not find workspace root with privacy-fingerprint-generator"))?;

    log::info!("[{}] Executing privacy tool from workspace: {:?}", timestamp, workspace_root);
    log::info!("[{}] Tool args: {:?}", timestamp, args);
    log::info!("[{}] About to execute cargo command", timestamp);

    let output = Command::new("cargo")
        .current_dir(workspace_root)
        .args(&["run", "--release", "-p", "privacy-fingerprint-generator", "--"])
        .args(&args)
        .output()?;

    log::info!("[{}] Cargo command completed", timestamp);

    if output.status.success() {
        let stdout = String::from_utf8_lossy(&output.stdout).to_string();
        log::info!("[{}] Privacy tool executed successfully", timestamp);
        log::debug!("[{}] Tool output: {}", timestamp, stdout);
        Ok(stdout)
    } else {
        let error = String::from_utf8_lossy(&output.stderr);
        log::error!("[{}] Privacy tool execution failed: {}", timestamp, error);
        log::error!("[{}] Exit code: {:?}", timestamp, output.status.code());
        Err(anyhow::anyhow!("Privacy tool execution failed: {}", error))
    }
}

// Helper function to parse JSON from tool output
fn parse_tool_output<T: for<'de> Deserialize<'de>>(output: &str) -> Result<T> {
    // Find the JSON part in the output (skip warning messages)
    let lines: Vec<&str> = output.lines().collect();
    for line in lines {
        if line.trim().starts_with('{') {
            return Ok(serde_json::from_str(line)?);
        }
    }
    Err(anyhow::anyhow!("No JSON found in tool output"))
}
