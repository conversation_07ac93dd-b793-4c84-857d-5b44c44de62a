use crate::{AppState, AppConfig, FingerprintHistoryEntry, execute_privacy_tool, parse_tool_output};
use crate::fingerprint::*;
use crate::privacy::*;
use serde::{Deserialize, Serialize};
use tauri::{State, Window};
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct GenerateFingerprintRequest {
    pub privacy_level: String,
    pub real_system: bool,
    pub count: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GenerateFingerprintResponse {
    pub success: bool,
    pub fingerprint: Option<PrivacyFingerprint>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn generate_fingerprint(
    request: GenerateFingerprintRequest,
    state: State<'_, AppState>,
    window: Window,
) -> Result<GenerateFingerprintResponse, String> {
    log::info!("Generating fingerprint with privacy level: {}", request.privacy_level);

    // Emit progress event
    window.emit("fingerprint_generation_started", ()).map_err(|e| e.to_string())?;

    // Try external tool first, fallback to mock generation
    let fingerprint = if request.real_system {
        match try_external_tool_generation(&request).await {
            Ok(fp) => fp,
            Err(_) => {
                log::warn!("External tool failed, falling back to mock generation");
                generate_mock_fingerprint(&request, request.real_system).await?
            }
        }
    } else {
        generate_mock_fingerprint(&request, false).await?
    };

    // Update app state
    {
        let mut current_fp = state.current_fingerprint.lock().await;
        *current_fp = Some(fingerprint.clone());
    }

    // Add to history
    {
        let mut history = state.fingerprint_history.lock().await;
        history.push(FingerprintHistoryEntry {
            id: fingerprint.id.clone(),
            timestamp: fingerprint.created_at.clone(),
            privacy_level: request.privacy_level.clone(),
            privacy_score: fingerprint.privacy_metadata.privacy_score,
            action: "generated".to_string(),
        });
    }

    window.emit("fingerprint_generation_completed", &fingerprint).map_err(|e| e.to_string())?;

    Ok(GenerateFingerprintResponse {
        success: true,
        fingerprint: Some(fingerprint),
        error: None,
    })
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RotateFingerprintResponse {
    pub success: bool,
    pub rotation_result: Option<FingerprintRotationResult>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn rotate_fingerprint(
    force: bool,
    state: State<'_, AppState>,
    window: Window,
) -> Result<RotateFingerprintResponse, String> {
    log::info!("Rotating fingerprint (force: {})", force);
    
    // Check if we have a current fingerprint
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if current_fp.is_none() {
        return Ok(RotateFingerprintResponse {
            success: false,
            rotation_result: None,
            error: Some("No current fingerprint to rotate".to_string()),
        });
    }
    
    window.emit("fingerprint_rotation_started", ()).map_err(|e| e.to_string())?;
    
    // Create temporary file for rotation
    let temp_path = std::env::temp_dir().join("current_fingerprint.json");
    let fp_json = serde_json::to_string_pretty(&current_fp.unwrap()).map_err(|e| e.to_string())?;
    std::fs::write(&temp_path, fp_json).map_err(|e| e.to_string())?;
    
    let mut args = vec![
        "--format", "json",
        "rotate",
        "--fingerprint-path", temp_path.to_str().unwrap(),
    ];
    
    if force {
        args.push("--force");
    }
    
    match execute_privacy_tool(args).await {
        Ok(output) => {
            match parse_tool_output::<FingerprintRotationResult>(&output) {
                Ok(rotation_result) => {
                    // Load the rotated fingerprint
                    if let Ok(rotated_fp_json) = std::fs::read_to_string(&temp_path) {
                        if let Ok(rotated_fp) = serde_json::from_str::<PrivacyFingerprint>(&rotated_fp_json) {
                            // Update app state
                            {
                                let mut current_fp = state.current_fingerprint.lock().await;
                                *current_fp = Some(rotated_fp.clone());
                            }
                            
                            // Add to history
                            {
                                let mut history = state.fingerprint_history.lock().await;
                                history.push(FingerprintHistoryEntry {
                                    id: rotated_fp.id.clone(),
                                    timestamp: rotated_fp.created_at.clone(),
                                    privacy_level: format!("{:?}", rotated_fp.privacy_level),
                                    privacy_score: rotated_fp.privacy_metadata.privacy_score,
                                    action: "rotated".to_string(),
                                });
                            }
                        }
                    }
                    
                    // Clean up temp file
                    let _ = std::fs::remove_file(&temp_path);
                    
                    window.emit("fingerprint_rotation_completed", &rotation_result).map_err(|e| e.to_string())?;
                    
                    Ok(RotateFingerprintResponse {
                        success: true,
                        rotation_result: Some(rotation_result),
                        error: None,
                    })
                }
                Err(e) => {
                    let error_msg = format!("Failed to parse rotation result: {}", e);
                    window.emit("fingerprint_rotation_failed", &error_msg).map_err(|e| e.to_string())?;
                    
                    Ok(RotateFingerprintResponse {
                        success: false,
                        rotation_result: None,
                        error: Some(error_msg),
                    })
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to rotate fingerprint: {}", e);
            window.emit("fingerprint_rotation_failed", &error_msg).map_err(|e| e.to_string())?;
            
            Ok(RotateFingerprintResponse {
                success: false,
                rotation_result: None,
                error: Some(error_msg),
            })
        }
    }
}

#[tauri::command]
pub async fn validate_fingerprint(
    state: State<'_, AppState>,
) -> Result<ValidationResult, String> {
    let current_fp = {
        let fp_guard = state.current_fingerprint.lock().await;
        fp_guard.clone()
    };
    
    if let Some(fingerprint) = current_fp {
        // Create temporary file for validation
        let temp_path = std::env::temp_dir().join("validate_fingerprint.json");
        let fp_json = serde_json::to_string_pretty(&fingerprint).map_err(|e| e.to_string())?;
        std::fs::write(&temp_path, fp_json).map_err(|e| e.to_string())?;
        
        let args = vec![
            "--format", "json",
            "validate",
            "--fingerprint-path", temp_path.to_str().unwrap(),
            "--check-uniqueness",
        ];
        
        match execute_privacy_tool(args).await {
            Ok(output) => {
                let _ = std::fs::remove_file(&temp_path);
                parse_tool_output::<ValidationResult>(&output).map_err(|e| e.to_string())
            }
            Err(e) => {
                let _ = std::fs::remove_file(&temp_path);
                Err(format!("Failed to validate fingerprint: {}", e))
            }
        }
    } else {
        Err("No fingerprint to validate".to_string())
    }
}

#[tauri::command]
pub async fn get_fingerprint_history(
    state: State<'_, AppState>,
) -> Result<Vec<FingerprintHistoryEntry>, String> {
    let history = state.fingerprint_history.lock().await;
    Ok(history.clone())
}

#[tauri::command]
pub async fn get_config(state: State<'_, AppState>) -> Result<AppConfig, String> {
    let config = state.config.lock().await;
    Ok(config.clone())
}

#[tauri::command]
pub async fn update_config(
    new_config: AppConfig,
    state: State<'_, AppState>,
) -> Result<(), String> {
    let mut config = state.config.lock().await;
    *config = new_config;
    Ok(())
}

#[tauri::command]
pub async fn reset_config(state: State<'_, AppState>) -> Result<AppConfig, String> {
    let mut config = state.config.lock().await;
    *config = AppConfig::default();
    Ok(config.clone())
}

#[tauri::command]
pub async fn check_rust_tool_availability() -> Result<bool, String> {
    let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC");
    log::info!("[{}] check_rust_tool_availability() called", timestamp);

    // For now, return false due to stack overflow issue in the tool
    // This allows the app to work with mock data while we fix the tool
    log::warn!("[{}] Rust tool has known issues, using mock mode", timestamp);
    log::info!("[{}] check_rust_tool_availability() returning false", timestamp);

    Ok(false)
}

// Helper function to try external tool generation
async fn try_external_tool_generation(request: &GenerateFingerprintRequest) -> Result<PrivacyFingerprint, String> {
    let count_str = request.count.to_string();
    let args = vec![
        "--privacy-level", &request.privacy_level,
        "--format", "json",
        "generate",
        "--count", &count_str,
    ];

    let mut final_args = args;
    if request.real_system {
        final_args.push("--real-system");
    }

    let output = execute_privacy_tool(final_args).await.map_err(|e| e.to_string())?;
    parse_tool_output::<PrivacyFingerprint>(&output).map_err(|e| e.to_string())
}

// Helper function to generate mock fingerprint
async fn generate_mock_fingerprint(request: &GenerateFingerprintRequest, use_real_data: bool) -> Result<PrivacyFingerprint, String> {
    let id = Uuid::new_v4().to_string();
    let now = Utc::now();
    let created_at = now.to_rfc3339();

    // Parse privacy level
    let privacy_level = match request.privacy_level.as_str() {
        "Low" => PrivacyLevel::Low,
        "Medium" => PrivacyLevel::Medium,
        "High" => PrivacyLevel::High,
        "Maximum" => PrivacyLevel::Maximum,
        _ => PrivacyLevel::High,
    };

    // Generate privacy score based on level
    let privacy_score = match privacy_level {
        PrivacyLevel::Low => 0.35,
        PrivacyLevel::Medium => 0.60,
        PrivacyLevel::High => 0.80,
        PrivacyLevel::Maximum => 0.92,
    };

    let fingerprint = if use_real_data {
        generate_real_system_fingerprint(id, created_at, privacy_level, privacy_score).await?
    } else {
        generate_mock_system_fingerprint(id, created_at, privacy_level, privacy_score).await?
    };

    Ok(fingerprint)
}

// Generate fingerprint using real system data
async fn generate_real_system_fingerprint(
    id: String,
    created_at: String,
    privacy_level: PrivacyLevel,
    privacy_score: f64,
) -> Result<PrivacyFingerprint, String> {
    use sysinfo::System;

    // Get comprehensive system information
    let mut sys = System::new_all();
    sys.refresh_all();

    let hostname = hostname::get()
        .map(|h| h.to_string_lossy().to_string())
        .unwrap_or_else(|_| "unknown".to_string());
    let username = whoami::username();
    let cpu_count = num_cpus::get();

    // Get detailed system info
    let cpu_brand = sys.global_cpu_info().brand();
    let memory_total = sys.total_memory();
    let os_version = format!("{} {}", std::env::consts::OS, std::env::consts::ARCH);

    // Create privacy-protected hashes
    let mut hasher = Sha256::new();
    hasher.update(hostname.as_bytes());
    let hostname_hash = format!("{:x}", hasher.finalize())[..16].to_string();

    let mut hasher = Sha256::new();
    hasher.update(username.as_bytes());
    let username_hash = format!("{:x}", hasher.finalize())[..16].to_string();

    let mut hasher = Sha256::new();
    hasher.update(format!("{}_{}", hostname, username).as_bytes());
    let device_id = format!("{:x}", hasher.finalize())[..32].to_string();

    // Hardware signature with privacy protection
    let hardware_signature = HardwareSignature {
        cpu_signature: match privacy_level {
            PrivacyLevel::Low => format!("{}_{}_cores", cpu_brand.split_whitespace().next().unwrap_or("cpu"), cpu_count),
            PrivacyLevel::Medium => format!("cpu_class_{}", if cpu_count <= 4 { "low" } else if cpu_count <= 8 { "mid" } else { "high" }),
            PrivacyLevel::High => format!("cpu_tier_{}", if cpu_count <= 2 { "1" } else if cpu_count <= 8 { "2" } else { "3" }),
            PrivacyLevel::Maximum => "cpu_class_obfuscated".to_string(),
        },
        memory_class: match privacy_level {
            PrivacyLevel::Low => format!("{}gb_class", memory_total / (1024 * 1024 * 1024)),
            PrivacyLevel::Medium => match memory_total / (1024 * 1024 * 1024) {
                0..=4 => "low_memory".to_string(),
                5..=16 => "mid_memory".to_string(),
                _ => "high_memory".to_string(),
            },
            PrivacyLevel::High => match memory_total / (1024 * 1024 * 1024) {
                0..=8 => "sufficient".to_string(),
                _ => "ample".to_string(),
            },
            PrivacyLevel::Maximum => "adequate".to_string(),
        },
        architecture: std::env::consts::ARCH.to_string(),
        hardware_uuid_hash: format!("{:x}", Sha256::digest(format!("hw_{}_{}", device_id, cpu_brand).as_bytes()))[..24].to_string(),
        performance_class: match (cpu_count, memory_total / (1024 * 1024 * 1024)) {
            (1..=2, 0..=4) => "basic".to_string(),
            (3..=8, 5..=16) => "standard".to_string(),
            (9.., 17..) => "high_performance".to_string(),
            _ => "standard".to_string(),
        },
    };

    // System signature
    let system_signature = SystemSignature {
        os_family: std::env::consts::FAMILY.to_string(),
        os_version_class: match privacy_level {
            PrivacyLevel::Low => format!("{}_{}", std::env::consts::OS,
                os_version.split_whitespace().next().unwrap_or("unknown")),
            PrivacyLevel::Medium => format!("{}_stable", std::env::consts::OS),
            PrivacyLevel::High => "modern_os".to_string(),
            PrivacyLevel::Maximum => "supported_os".to_string(),
        },
        locale_region: match privacy_level {
            PrivacyLevel::Low => std::env::var("LANG").unwrap_or_else(|_| "en_US".to_string()),
            _ => "en_US".to_string(), // Obfuscated for privacy
        },
        timezone_offset: match privacy_level {
            PrivacyLevel::Low => chrono::Local::now().offset().local_minus_utc(),
            _ => 0, // Obfuscated for privacy
        },
        hostname_hash,
        username_hash,
    };

    create_complete_fingerprint(id, created_at, privacy_level, privacy_score, device_id, hardware_signature, system_signature)
}

// Generate mock fingerprint for testing
async fn generate_mock_system_fingerprint(
    id: String,
    created_at: String,
    privacy_level: PrivacyLevel,
    privacy_score: f64,
) -> Result<PrivacyFingerprint, String> {
    let device_id = format!("mock_device_{}", Uuid::new_v4().to_string()[..8].to_string());

    let hardware_signature = HardwareSignature {
        cpu_signature: "mock_cpu_x64".to_string(),
        memory_class: "8gb_class".to_string(),
        architecture: "x86_64".to_string(),
        hardware_uuid_hash: format!("{:x}", Sha256::digest("mock_hardware".as_bytes()))[..24].to_string(),
        performance_class: "standard".to_string(),
    };

    let system_signature = SystemSignature {
        os_family: "unix".to_string(),
        os_version_class: "modern_os".to_string(),
        locale_region: "en_US".to_string(),
        timezone_offset: 0,
        hostname_hash: format!("{:x}", Sha256::digest("mock_host".as_bytes()))[..16].to_string(),
        username_hash: format!("{:x}", Sha256::digest("mock_user".as_bytes()))[..16].to_string(),
    };

    create_complete_fingerprint(id, created_at, privacy_level, privacy_score, device_id, hardware_signature, system_signature)
}

// Helper function to create complete fingerprint structure
fn create_complete_fingerprint(
    id: String,
    created_at: String,
    privacy_level: PrivacyLevel,
    privacy_score: f64,
    device_id: String,
    hardware_signature: HardwareSignature,
    system_signature: SystemSignature,
) -> Result<PrivacyFingerprint, String> {
    // VS Code signature (mock for now)
    let vscode_signature = VSCodeSignature {
        version_class: "stable".to_string(),
        machine_id_hash: format!("{:x}", Sha256::digest(format!("vscode_{}", device_id).as_bytes()))[..32].to_string(),
        session_signature: format!("{:x}", Sha256::digest(format!("session_{}", chrono::Utc::now().timestamp()).as_bytes()))[..16].to_string(),
        workspace_signature: Some(format!("{:x}", Sha256::digest("workspace_default".as_bytes()))[..16].to_string()),
        extensions_signature: format!("{:x}", Sha256::digest("extensions_standard".as_bytes()))[..24].to_string(),
    };

    // Network signature (privacy-protected)
    let network_signature = NetworkSignature {
        interface_count: 2, // Typical for most systems
        mac_signature: format!("{:x}", Sha256::digest(format!("mac_{}", device_id).as_bytes()))[..16].to_string(),
        network_class: "standard".to_string(),
        connectivity_signature: format!("{:x}", Sha256::digest("connectivity_wifi".as_bytes()))[..12].to_string(),
    };

    // Privacy metadata
    let privacy_metadata = PrivacyMetadata {
        anonymization_applied: vec![
            "hostname_hashing".to_string(),
            "username_hashing".to_string(),
            "hardware_classification".to_string(),
            "network_obfuscation".to_string(),
        ],
        obfuscation_methods: match privacy_level {
            PrivacyLevel::Low => vec!["basic_hashing".to_string()],
            PrivacyLevel::Medium => vec!["enhanced_hashing".to_string(), "data_classification".to_string()],
            PrivacyLevel::High => vec!["strong_anonymization".to_string(), "data_obfuscation".to_string()],
            PrivacyLevel::Maximum => vec!["differential_privacy".to_string(), "maximum_obfuscation".to_string()],
        },
        data_retention_policy: "educational_use_only".to_string(),
        privacy_score,
        tracking_resistance: privacy_score * 0.9, // Slightly lower than privacy score
    };

    // Generate final fingerprint hash
    let fingerprint_data = format!("{}{}{:?}{}", id, device_id, privacy_level, created_at);
    let fingerprint_hash = format!("{:x}", Sha256::digest(fingerprint_data.as_bytes()));

    // Determine rotation settings
    let rotation_enabled = matches!(privacy_level, PrivacyLevel::High | PrivacyLevel::Maximum);
    let next_rotation = if rotation_enabled {
        let next_rotation_time = chrono::Utc::now() + chrono::Duration::hours(24);
        Some(next_rotation_time.to_rfc3339())
    } else {
        None
    };

    Ok(PrivacyFingerprint {
        id,
        version: "1.0.0".to_string(),
        created_at,
        privacy_level,
        rotation_enabled,
        next_rotation,
        device_id,
        hardware_signature,
        system_signature,
        vscode_signature,
        network_signature,
        privacy_metadata,
        fingerprint_hash,
    })
}
