[package]
name = "privacy-fingerprint-ui"
version = "1.0.0"
description = "Privacy-focused fingerprint generator desktop application"
authors = ["Security Research Team"]
license = "MIT"
repository = "https://github.com/security-research/privacy-fingerprint-ui"
edition = "2021"

# Prevent this from being part of the parent workspace
[workspace]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.0", features = [] }

[dependencies]
tauri = { version = "1.5.4", features = [
  "dialog-ask",
  "dialog-confirm",
  "dialog-message",
  "dialog-open",
  "dialog-save",
  "fs-copy-file",
  "fs-create-dir",
  "fs-exists",
  "fs-read-dir",
  "fs-read-file",
  "fs-remove-dir",
  "fs-remove-file",
  "fs-rename-file",
  "fs-write-file",
  "notification-all",
  "path-all",
  "shell-execute",
  "shell-open",
  "system-tray",
  "window-close",
  "window-hide",
  "window-maximize",
  "window-minimize",
  "window-show",
  "window-start-dragging",
  "window-unmaximize",
  "window-unminimize"
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
thiserror = "1.0"
uuid = { version = "1.6", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
dirs = "5.0"
log = "0.4"
env_logger = "0.10"

# File system operations
fs_extra = "1.3"

# Process execution
which = "4.4"

# System information
hostname = "0.3"
whoami = "1.4"
num_cpus = "1.16"
sysinfo = "0.30"

# Cryptography
sha2 = "0.10"

[features]
# This feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[[bin]]
name = "privacy-fingerprint-ui"
path = "src/main.rs"
