# Rust
/target/
**/*.rs.bk
Cargo.lock

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# VS Code
.vscode/
*.code-workspace

# Nix
result
result-*

# Fingerprints and profiles (sensitive data)
.fingerprints/
/tmp/vscode-fingerprint-profiles/

# Privacy data
*.fingerprint.json
*_fingerprint.json
privacy_report.md

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Logs
*.log
debug_output.log

# Build artifacts
dist/
build/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Nix development
.direnv/
.envrc

# Tauri
src-tauri/target/
