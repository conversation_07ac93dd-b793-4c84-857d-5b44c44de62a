//! VS Code profile detection and analysis utilities.
//!
//! This module provides functionality to detect, analyze, and validate VS Code
//! profile configurations for security research purposes.

use crate::{VSCodeProfile, ProfilePersistenceAnalysis, PersistenceLevel, TrialDataLocation};
use anyhow::{Result, Context};
use std::path::{Path, PathBuf};
use std::fs;
use chrono::{Utc, DateTime};
use walkdir::WalkDir;

/// VS Code profile detector for educational analysis
pub struct ProfileDetector {
    mock_mode: bool,
}

impl ProfileDetector {
    /// Create a new profile detector
    pub fn new(mock_mode: bool) -> Self {
        Self { mock_mode }
    }

    /// Detect the current VS Code profile configuration
    pub async fn detect_current_profile(&self) -> Result<VSCodeProfile> {
        if self.mock_mode {
            Ok(self.create_mock_profile())
        } else {
            self.detect_real_profile().await
        }
    }

    /// Create a mock profile for testing
    fn create_mock_profile(&self) -> VSCodeProfile {
        VSCodeProfile {
            profile_name: "Default".to_string(),
            profile_path: "/Users/<USER>/Library/Application Support/Code".to_string(),
            is_default: true,
            is_temporary: false,
            user_data_dir: "/Users/<USER>/Library/Application Support/Code".to_string(),
            extensions_dir: "/Users/<USER>/.vscode/extensions".to_string(),
            global_storage_dir: "/Users/<USER>/Library/Application Support/Code/User/globalStorage".to_string(),
            workspace_storage_dir: "/Users/<USER>/Library/Application Support/Code/User/workspaceStorage".to_string(),
            created_at: Some(Utc::now() - chrono::Duration::days(30)),
            last_used: Some(Utc::now()),
        }
    }

    /// Detect real VS Code profile from the file system
    async fn detect_real_profile(&self) -> Result<VSCodeProfile> {
        tracing::info!("Detecting real VS Code profile configuration...");

        // Get the default VS Code user data directory
        let user_data_dir = self.get_default_user_data_dir()?;

        // Check if VS Code is using a custom profile
        let current_profile = self.detect_current_profile_from_processes()
            .unwrap_or_else(|| self.detect_default_profile(&user_data_dir));

        // Validate the profile exists and is accessible
        self.validate_profile_access(&current_profile)?;

        Ok(current_profile)
    }

    /// Get the default VS Code user data directory for the current platform
    fn get_default_user_data_dir(&self) -> Result<PathBuf> {
        let home_dir = dirs::home_dir()
            .context("Could not determine home directory")?;

        #[cfg(target_os = "macos")]
        let user_data_dir = home_dir.join("Library/Application Support/Code");

        #[cfg(target_os = "windows")]
        let user_data_dir = home_dir.join("AppData/Roaming/Code");

        #[cfg(target_os = "linux")]
        let user_data_dir = home_dir.join(".config/Code");

        Ok(user_data_dir)
    }

    /// Detect current profile from running VS Code processes
    fn detect_current_profile_from_processes(&self) -> Option<VSCodeProfile> {
        use sysinfo::{System, Process};

        let mut system = System::new_all();
        system.refresh_all();

        // Look for VS Code processes with custom user-data-dir arguments
        for (_, process) in system.processes() {
            let name = process.name();
            if name.contains("code") || name.contains("Code") {
                let cmd = process.cmd();

                // Check for --user-data-dir argument
                for (i, arg) in cmd.iter().enumerate() {
                    if arg == "--user-data-dir" && i + 1 < cmd.len() {
                        let user_data_dir = &cmd[i + 1];
                        return Some(self.create_profile_from_path(user_data_dir));
                    }
                }
            }
        }

        None
    }

    /// Create a profile configuration from a user data directory path
    fn create_profile_from_path(&self, user_data_dir: &str) -> VSCodeProfile {
        let path = PathBuf::from(user_data_dir);
        let is_temporary = self.is_temporary_path(user_data_dir);
        let is_default = self.is_default_path(user_data_dir);

        let profile_name = if is_default {
            "Default".to_string()
        } else if is_temporary {
            "Temporary".to_string()
        } else {
            path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("Custom")
                .to_string()
        };

        VSCodeProfile {
            profile_name,
            profile_path: user_data_dir.to_string(),
            is_default,
            is_temporary,
            user_data_dir: user_data_dir.to_string(),
            extensions_dir: self.get_extensions_dir(user_data_dir),
            global_storage_dir: format!("{}/User/globalStorage", user_data_dir),
            workspace_storage_dir: format!("{}/User/workspaceStorage", user_data_dir),
            created_at: self.get_directory_created_time(&path),
            last_used: self.get_directory_modified_time(&path),
        }
    }

    /// Detect the default profile configuration
    fn detect_default_profile(&self, user_data_dir: &PathBuf) -> VSCodeProfile {
        let user_data_str = user_data_dir.to_string_lossy().to_string();
        self.create_profile_from_path(&user_data_str)
    }

    /// Check if a path is a temporary directory
    fn is_temporary_path(&self, path: &str) -> bool {
        path.contains("/tmp") ||
        path.contains("/temp") ||
        path.contains("Temp") ||
        path.contains("temp-")
    }

    /// Check if a path is the default VS Code directory
    fn is_default_path(&self, path: &str) -> bool {
        path.contains("Application Support/Code") ||
        path.contains("AppData/Roaming/Code") ||
        path.contains(".config/Code")
    }

    /// Get the extensions directory for a given user data directory
    fn get_extensions_dir(&self, user_data_dir: &str) -> String {
        // Check if there's a custom extensions directory
        let extensions_in_profile = format!("{}/extensions", user_data_dir);
        if PathBuf::from(&extensions_in_profile).exists() {
            extensions_in_profile
        } else {
            // Use the default global extensions directory
            dirs::home_dir()
                .map(|home| {
                    #[cfg(target_os = "macos")]
                    let ext_dir = home.join(".vscode/extensions");

                    #[cfg(target_os = "windows")]
                    let ext_dir = home.join(".vscode/extensions");

                    #[cfg(target_os = "linux")]
                    let ext_dir = home.join(".vscode/extensions");

                    ext_dir.to_string_lossy().to_string()
                })
                .unwrap_or_else(|| format!("{}/.vscode/extensions", user_data_dir))
        }
    }

    /// Get directory creation time
    fn get_directory_created_time(&self, path: &PathBuf) -> Option<DateTime<Utc>> {
        fs::metadata(path)
            .ok()
            .and_then(|metadata| metadata.created().ok())
            .map(|time| DateTime::from(time))
    }

    /// Get directory last modified time
    fn get_directory_modified_time(&self, path: &PathBuf) -> Option<DateTime<Utc>> {
        fs::metadata(path)
            .ok()
            .and_then(|metadata| metadata.modified().ok())
            .map(|time| DateTime::from(time))
    }

    /// Validate that the profile directory is accessible
    fn validate_profile_access(&self, profile: &VSCodeProfile) -> Result<()> {
        let user_data_path = PathBuf::from(&profile.user_data_dir);

        if !user_data_path.exists() {
            tracing::warn!("Profile directory does not exist: {}", profile.user_data_dir);
            return Ok(()); // Don't fail, just warn
        }

        if !user_data_path.is_dir() {
            anyhow::bail!("Profile path is not a directory: {}", profile.user_data_dir);
        }

        // Check if we can read the directory
        match fs::read_dir(&user_data_path) {
            Ok(_) => {
                tracing::info!("Successfully validated profile access: {}", profile.profile_name);
                Ok(())
            }
            Err(e) => {
                tracing::warn!("Limited access to profile directory: {}", e);
                Ok(()) // Don't fail, just warn about limited access
            }
        }
    }

    /// Analyze profile persistence characteristics
    pub async fn analyze_persistence(&self, profile: &VSCodeProfile) -> Result<ProfilePersistenceAnalysis> {
        let trial_data_locations = self.detect_trial_data_locations(profile).await?;
        
        let persistence_level = if profile.is_temporary {
            PersistenceLevel::Session
        } else {
            PersistenceLevel::System
        };
        
        let bypass_resistance_score = self.calculate_bypass_resistance(&trial_data_locations);
        
        let recommendations = vec![
            "Use temporary profiles for isolated trial testing".to_string(),
            "Consider server-side validation impact on testing".to_string(),
            "Multiple storage locations increase persistence".to_string(),
        ];

        Ok(ProfilePersistenceAnalysis {
            current_profile: profile.clone(),
            persistence_level,
            trial_data_locations,
            cross_session_persistence: !profile.is_temporary,
            cross_profile_persistence: false,
            server_side_tracking: true,
            bypass_resistance_score,
            recommendations,
        })
    }

    /// Detect real trial data storage locations
    async fn detect_trial_data_locations(&self, profile: &VSCodeProfile) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();

        if self.mock_mode {
            // Return mock data for safety
            return Ok(self.create_mock_trial_locations(profile));
        }

        // Scan global storage directory for extension data
        if let Ok(global_locations) = self.scan_global_storage(&profile.global_storage_dir).await {
            locations.extend(global_locations);
        }

        // Scan workspace storage directory
        if let Ok(workspace_locations) = self.scan_workspace_storage(&profile.workspace_storage_dir).await {
            locations.extend(workspace_locations);
        }

        // Check for secrets storage
        if let Ok(secrets_locations) = self.scan_secrets_storage(&profile.user_data_dir).await {
            locations.extend(secrets_locations);
        }

        // Check for state storage
        if let Ok(state_locations) = self.scan_state_storage(&profile.user_data_dir).await {
            locations.extend(state_locations);
        }

        // Check for extension-specific storage
        if let Ok(extension_locations) = self.scan_extension_storage(&profile.extensions_dir).await {
            locations.extend(extension_locations);
        }

        Ok(locations)
    }

    /// Create mock trial data locations for safety
    fn create_mock_trial_locations(&self, profile: &VSCodeProfile) -> Vec<TrialDataLocation> {
        vec![
            TrialDataLocation {
                location_type: "Global Storage".to_string(),
                path: format!("{}/augment.vscode-augment", profile.global_storage_dir),
                persistence_level: PersistenceLevel::System,
                encrypted: false,
                size_bytes: 1024,
                contains_trial_data: true,
                contains_auth_data: true,
                last_modified: Some(Utc::now()),
            },
            TrialDataLocation {
                location_type: "Secrets Storage".to_string(),
                path: format!("{}/secrets", profile.user_data_dir),
                persistence_level: PersistenceLevel::System,
                encrypted: true,
                size_bytes: 512,
                contains_trial_data: false,
                contains_auth_data: true,
                last_modified: Some(Utc::now()),
            },
        ]
    }

    /// Scan global storage directory for trial-related data
    async fn scan_global_storage(&self, global_storage_dir: &str) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();
        let global_path = PathBuf::from(global_storage_dir);

        if !global_path.exists() {
            return Ok(locations);
        }

        // Look for extension storage directories
        for entry in WalkDir::new(&global_path).max_depth(2) {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() {
                let dir_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("");

                // Check if this looks like an extension storage directory
                if self.is_extension_storage_dir(dir_name) {
                    if let Ok(location) = self.analyze_storage_location(path, "Global Storage").await {
                        locations.push(location);
                    }
                }
            }
        }

        Ok(locations)
    }

    /// Scan workspace storage directory
    async fn scan_workspace_storage(&self, workspace_storage_dir: &str) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();
        let workspace_path = PathBuf::from(workspace_storage_dir);

        if !workspace_path.exists() {
            return Ok(locations);
        }

        // Look for workspace-specific storage
        for entry in WalkDir::new(&workspace_path).max_depth(2) {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() && self.is_trial_related_file(path) {
                if let Ok(location) = self.analyze_storage_location(path, "Workspace Storage").await {
                    locations.push(location);
                }
            }
        }

        Ok(locations)
    }

    /// Scan secrets storage
    async fn scan_secrets_storage(&self, user_data_dir: &str) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();
        let secrets_path = PathBuf::from(user_data_dir).join("User").join("secrets.json");

        if secrets_path.exists() {
            if let Ok(location) = self.analyze_storage_location(&secrets_path, "Secrets Storage").await {
                locations.push(location);
            }
        }

        Ok(locations)
    }

    /// Scan state storage
    async fn scan_state_storage(&self, user_data_dir: &str) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();
        let state_path = PathBuf::from(user_data_dir).join("User").join("state");

        if state_path.exists() {
            for entry in WalkDir::new(&state_path).max_depth(1) {
                let entry = entry?;
                let path = entry.path();

                if path.is_file() && self.is_trial_related_file(path) {
                    if let Ok(location) = self.analyze_storage_location(path, "State Storage").await {
                        locations.push(location);
                    }
                }
            }
        }

        Ok(locations)
    }

    /// Scan extension storage directories
    async fn scan_extension_storage(&self, extensions_dir: &str) -> Result<Vec<TrialDataLocation>> {
        let mut locations = Vec::new();
        let extensions_path = PathBuf::from(extensions_dir);

        if !extensions_path.exists() {
            return Ok(locations);
        }

        // Look for extension-specific storage files
        for entry in WalkDir::new(&extensions_path).max_depth(3) {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() && self.is_trial_related_file(path) {
                if let Ok(location) = self.analyze_storage_location(path, "Extension Storage").await {
                    locations.push(location);
                }
            }
        }

        Ok(locations)
    }

    /// Check if a directory name looks like extension storage
    fn is_extension_storage_dir(&self, dir_name: &str) -> bool {
        // Common patterns for extension storage directories
        dir_name.contains("augment") ||
        dir_name.contains("trial") ||
        dir_name.contains("subscription") ||
        dir_name.contains("auth") ||
        dir_name.contains("license") ||
        dir_name.contains(".") // Extension IDs typically contain dots
    }

    /// Check if a file is trial-related
    fn is_trial_related_file(&self, path: &Path) -> bool {
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");

        let extension = path.extension()
            .and_then(|e| e.to_str())
            .unwrap_or("");

        // Check file name patterns
        let name_matches = file_name.contains("trial") ||
            file_name.contains("subscription") ||
            file_name.contains("auth") ||
            file_name.contains("license") ||
            file_name.contains("fingerprint") ||
            file_name.contains("augment");

        // Check file extensions
        let ext_matches = extension == "json" ||
            extension == "db" ||
            extension == "sqlite" ||
            extension == "log";

        name_matches && ext_matches
    }

    /// Analyze a storage location to determine its properties
    async fn analyze_storage_location(&self, path: &Path, location_type: &str) -> Result<TrialDataLocation> {
        let metadata = fs::metadata(path)?;
        let size_bytes = metadata.len();
        let last_modified = metadata.modified()
            .ok()
            .map(|time| DateTime::from(time));

        // Determine persistence level based on path
        let persistence_level = if self.is_temporary_path(&path.to_string_lossy()) {
            PersistenceLevel::Temporary
        } else if path.to_string_lossy().contains("workspace") {
            PersistenceLevel::Profile
        } else {
            PersistenceLevel::System
        };

        // Check if file contains trial or auth data
        let (contains_trial_data, contains_auth_data, encrypted) =
            self.analyze_file_content(path).await.unwrap_or((false, false, false));

        Ok(TrialDataLocation {
            location_type: location_type.to_string(),
            path: path.to_string_lossy().to_string(),
            persistence_level,
            encrypted,
            size_bytes,
            contains_trial_data,
            contains_auth_data,
            last_modified,
        })
    }

    /// Analyze file content to determine what type of data it contains
    async fn analyze_file_content(&self, path: &Path) -> Result<(bool, bool, bool)> {
        // For safety and privacy, we'll only do basic analysis
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");

        // Determine content type based on file name and extension
        let contains_trial_data = file_name.contains("trial") ||
            file_name.contains("subscription") ||
            file_name.contains("license");

        let contains_auth_data = file_name.contains("auth") ||
            file_name.contains("token") ||
            file_name.contains("secret");

        // Assume encrypted if it's in secrets storage or has certain patterns
        let encrypted = path.to_string_lossy().contains("secrets") ||
            file_name.contains("encrypted") ||
            file_name.contains("secure");

        Ok((contains_trial_data, contains_auth_data, encrypted))
    }

    /// Calculate bypass resistance score based on storage locations
    fn calculate_bypass_resistance(&self, locations: &[TrialDataLocation]) -> f64 {
        let mut score = 0.0;
        
        for location in locations {
            match location.persistence_level {
                PersistenceLevel::Temporary => score += 0.1,
                PersistenceLevel::Session => score += 0.2,
                PersistenceLevel::Profile => score += 0.4,
                PersistenceLevel::System => score += 0.6,
                PersistenceLevel::Server => score += 0.8,
            }
            
            if location.encrypted {
                score += 0.2;
            }
            
            if location.contains_trial_data {
                score += 0.1;
            }
            
            if location.contains_auth_data {
                score += 0.1;
            }
        }
        
        (score / locations.len() as f64).min(1.0)
    }

    /// Generate profile recommendations for different testing scenarios
    pub fn generate_recommendations(&self, scenario: &str) -> Vec<String> {
        match scenario {
            "trial" => vec![
                "Use default persistent profile for realistic trial experience".to_string(),
                "Monitor trial data persistence across VS Code restarts".to_string(),
                "Test server-side validation behavior".to_string(),
            ],
            "bypass" => vec![
                "Use isolated temporary profile for ethical research".to_string(),
                "Document all testing procedures for educational purposes".to_string(),
                "Respect software licensing and terms of service".to_string(),
            ],
            "isolation" => vec![
                "Use custom user data directory for complete isolation".to_string(),
                "Disable network access to prevent server-side tracking".to_string(),
                "Clean up test environments after research".to_string(),
            ],
            _ => vec![
                "Choose appropriate profile based on research goals".to_string(),
                "Always use ethical testing practices".to_string(),
                "Document findings for educational benefit".to_string(),
            ],
        }
    }

    /// Validate profile configuration for security research
    pub fn validate_profile(&self, profile: &VSCodeProfile) -> Vec<String> {
        let mut issues = Vec::new();
        
        if profile.is_default && !profile.is_temporary {
            issues.push("Using default persistent profile may affect production environment".to_string());
        }
        
        if !Path::new(&profile.user_data_dir).exists() {
            issues.push("User data directory does not exist".to_string());
        }
        
        if !Path::new(&profile.extensions_dir).exists() {
            issues.push("Extensions directory does not exist".to_string());
        }
        
        if profile.is_temporary && profile.user_data_dir.contains("/tmp") {
            issues.push("Temporary profile detected - data may not persist".to_string());
        }
        
        issues
    }
}

/// Utility functions for profile management
pub mod profile_utils {
    use super::*;

    /// Generate a command line for creating an isolated profile
    pub fn generate_isolated_profile_command(profile_name: &str) -> String {
        format!("code --user-data-dir=/tmp/vscode-{} --extensions-dir=/tmp/vscode-{}/extensions", profile_name, profile_name)
    }

    /// Generate a command line for creating a temporary profile
    pub fn generate_temporary_profile_command() -> String {
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        format!("code --user-data-dir=/tmp/vscode-temp-{}", timestamp)
    }

    /// Check if a path is likely a temporary directory
    pub fn is_temporary_path(path: &str) -> bool {
        path.contains("/tmp") || path.contains("/temp") || path.contains("Temp")
    }

    /// Extract profile name from user data directory path
    pub fn extract_profile_name(user_data_dir: &str) -> String {
        Path::new(user_data_dir)
            .file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("Unknown")
            .to_string()
    }
}
