[package]
name = "shared"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
homepage.workspace = true
documentation.workspace = true
keywords.workspace = true
categories.workspace = true
description = "Shared utilities and types for VS Code extension security research tools"

[dependencies]
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
sha2.workspace = true
uuid.workspace = true
hex.workspace = true
dirs.workspace = true
walkdir.workspace = true
chrono.workspace = true
colored.workspace = true
sysinfo = "0.30"

[dev-dependencies]
proptest.workspace = true
tempfile.workspace = true
