//! VS Code Profile Validator
//!
//! An educational tool that validates VS Code profile configurations and analyzes
//! how different profile setups affect trial tracking persistence. This tool helps
//! security researchers understand the impact of profile isolation on trial mechanisms.

use anyhow::Result;
use clap::{Parser, Subcommand};
use colored::Colorize;
use serde::{Deserialize, Serialize};
use std::path::Path;
use shared::{
    init, <PERSON><PERSON><PERSON><PERSON>, VSCodeProfile, ProfilePersistenceAnalysis, PersistenceLevel,
    TrialDataLocation, ToolConfig, OutputFormat, display_results, show_info, 
    show_warning, show_success, display_educational_info,
};

#[derive(Parser)]
#[command(name = "profile-validator")]
#[command(about = "Educational VS Code profile and trial persistence validator")]
#[command(version = shared::VERSION)]
struct Cli {
    #[command(subcommand)]
    command: Commands,

    /// Output format (console, json, markdown, html)
    #[arg(short, long, default_value = "console")]
    format: String,

    /// Enable verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Use mock data for safety (recommended)
    #[arg(short, long)]
    mock_mode: bool,

    /// Save results to file
    #[arg(short, long)]
    output: Option<String>,
}

#[derive(Subcommand)]
enum Commands {
    /// Detect current VS Code profile configuration
    DetectProfile {
        /// Show detailed profile information
        #[arg(short, long)]
        detailed: bool,
    },
    /// Validate trial data persistence across profiles
    ValidatePersistence {
        /// Test cross-profile persistence
        #[arg(short, long)]
        cross_profile: bool,
        /// Test server-side persistence
        #[arg(short, long)]
        server_side: bool,
    },
    /// Analyze profile impact on trial tracking
    AnalyzeImpact {
        /// Compare multiple profile configurations
        #[arg(short, long)]
        compare: bool,
    },
    /// Generate profile recommendations for testing
    Recommend {
        /// Testing scenario (trial, bypass, isolation)
        #[arg(short, long, default_value = "trial")]
        scenario: String,
    },
    /// Simulate profile switching effects
    SimulateSwitch {
        /// Source profile name
        #[arg(short, long)]
        from: Option<String>,
        /// Target profile name
        #[arg(short, long)]
        to: Option<String>,
    },
    /// Explain profile concepts
    Explain {
        /// Topic to explain (profiles, persistence, isolation)
        topic: String,
    },
}

#[derive(Debug, Serialize, Deserialize)]
struct ProfileValidationResult {
    current_profile: VSCodeProfile,
    persistence_analysis: ProfilePersistenceAnalysis,
    validation_tests: Vec<ValidationTest>,
    security_assessment: SecurityAssessment,
    recommendations: Vec<String>,
    educational_notes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct ValidationTest {
    test_name: String,
    test_type: String,
    passed: bool,
    details: String,
    impact: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct SecurityAssessment {
    bypass_resistance: f64,
    isolation_effectiveness: f64,
    persistence_strength: f64,
    overall_security_score: f64,
    vulnerabilities: Vec<String>,
    strengths: Vec<String>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize shared library with ethics check
    init()?;

    let cli = Cli::parse();

    // Parse output format
    let output_format = match cli.format.as_str() {
        "json" => OutputFormat::Json,
        "markdown" => OutputFormat::Markdown,
        "html" => OutputFormat::Html,
        _ => OutputFormat::Console,
    };

    let config = ToolConfig {
        tool_name: "profile-validator".to_string(),
        version: shared::VERSION.to_string(),
        output_format,
        verbose: cli.verbose,
        mock_mode: cli.mock_mode,
        educational_mode: true,
    };

    // Display educational information
    display_educational_info(
        "VS Code Profile Validation",
        "This tool analyzes VS Code profile configurations and their impact on trial tracking persistence.",
        &[
            "Use temporary profiles for isolated testing",
            "Default profiles provide realistic trial experience",
            "Server-side tracking persists across profile changes",
        ]
    );

    // Log usage for educational analysis
    EthicsChecker::log_usage("profile-validator", "validation");

    match cli.command {
        Commands::DetectProfile { detailed } => {
            let result = detect_current_profile(cli.mock_mode, detailed).await?;
            display_results(&result, &config)?;
            
            if let Some(output_path) = cli.output {
                shared::save_results(&result, &Path::new(&output_path), &config.output_format)?;
            }
        }
        Commands::ValidatePersistence { cross_profile, server_side } => {
            let analysis = validate_trial_persistence(cli.mock_mode, cross_profile, server_side).await?;
            display_results(&analysis, &config)?;
        }
        Commands::AnalyzeImpact { compare } => {
            let impact = analyze_profile_impact(cli.mock_mode, compare).await?;
            display_results(&impact, &config)?;
        }
        Commands::Recommend { scenario } => {
            let recommendations = generate_profile_recommendations(&scenario).await?;
            display_results(&recommendations, &config)?;
        }
        Commands::SimulateSwitch { from, to } => {
            simulate_profile_switch(from, to, cli.mock_mode).await?;
        }
        Commands::Explain { topic } => {
            explain_profile_concept(&topic);
        }
    }

    Ok(())
}

async fn detect_current_profile(mock_mode: bool, detailed: bool) -> Result<ProfileValidationResult> {
    show_info("🔍 Detecting current VS Code profile configuration...");

    EthicsChecker::verify_educational_operation("detect VS Code profile")?;

    let current_profile = if mock_mode {
        show_info("Using mock data for safety (mock mode enabled)");
        create_mock_profile()
    } else {
        show_info("Performing real profile detection...");
        detect_real_profile().await?
    };

    // Analyze persistence characteristics
    let persistence_analysis = analyze_profile_persistence(&current_profile, mock_mode).await?;
    
    // Run validation tests
    let validation_tests = run_profile_validation_tests(&current_profile, mock_mode).await?;
    
    // Assess security implications
    let security_assessment = assess_profile_security(&current_profile, &persistence_analysis);
    
    // Generate recommendations
    let recommendations = generate_recommendations(&current_profile, &persistence_analysis);
    
    let educational_notes = vec![
        "VS Code profiles determine where extension data is stored".to_string(),
        "Default profiles use persistent system directories".to_string(),
        "Temporary profiles can provide better isolation for testing".to_string(),
        "Server-side validation may persist across profile changes".to_string(),
        "Profile switching affects local storage but not server records".to_string(),
    ];

    if detailed {
        display_detailed_profile_info(&current_profile);
    }

    show_success(&format!(
        "✅ Profile detection complete: {} ({})", 
        current_profile.profile_name,
        if current_profile.is_temporary { "Temporary" } else { "Persistent" }
    ));

    Ok(ProfileValidationResult {
        current_profile,
        persistence_analysis,
        validation_tests,
        security_assessment,
        recommendations,
        educational_notes,
    })
}

fn create_mock_profile() -> VSCodeProfile {
    use chrono::Utc;
    
    VSCodeProfile {
        profile_name: "Default".to_string(),
        profile_path: "/Users/<USER>/Library/Application Support/Code".to_string(),
        is_default: true,
        is_temporary: false,
        user_data_dir: "/Users/<USER>/Library/Application Support/Code".to_string(),
        extensions_dir: "/Users/<USER>/.vscode/extensions".to_string(),
        global_storage_dir: "/Users/<USER>/Library/Application Support/Code/User/globalStorage".to_string(),
        workspace_storage_dir: "/Users/<USER>/Library/Application Support/Code/User/workspaceStorage".to_string(),
        created_at: Some(Utc::now() - chrono::Duration::days(30)),
        last_used: Some(Utc::now()),
    }
}

async fn detect_real_profile() -> Result<VSCodeProfile> {
    use shared::ProfileDetector;

    let detector = ProfileDetector::new(false); // Real mode
    detector.detect_current_profile().await
}

async fn analyze_profile_persistence(
    profile: &VSCodeProfile,
    mock_mode: bool
) -> Result<ProfilePersistenceAnalysis> {
    show_info("📊 Analyzing profile persistence characteristics...");

    use shared::ProfileDetector;

    let detector = ProfileDetector::new(mock_mode);
    detector.analyze_persistence(profile).await
}





async fn run_profile_validation_tests(
    profile: &VSCodeProfile,
    _mock_mode: bool
) -> Result<Vec<ValidationTest>> {
    let mut tests = Vec::new();

    // Test 1: Profile persistence
    tests.push(ValidationTest {
        test_name: "Profile Persistence Test".to_string(),
        test_type: "Persistence".to_string(),
        passed: !profile.is_temporary,
        details: format!("Profile type: {}", if profile.is_temporary { "Temporary" } else { "Persistent" }),
        impact: "Determines if trial data survives VS Code restarts".to_string(),
    });

    // Test 2: Extension directory accessibility
    tests.push(ValidationTest {
        test_name: "Extension Directory Access".to_string(),
        test_type: "Storage".to_string(),
        passed: std::path::Path::new(&profile.extensions_dir).exists(),
        details: format!("Extensions directory: {}", profile.extensions_dir),
        impact: "Extension installation and data persistence".to_string(),
    });

    // Test 3: Global storage accessibility
    tests.push(ValidationTest {
        test_name: "Global Storage Access".to_string(),
        test_type: "Storage".to_string(),
        passed: std::path::Path::new(&profile.global_storage_dir).exists(),
        details: format!("Global storage: {}", profile.global_storage_dir),
        impact: "Trial data and authentication persistence".to_string(),
    });

    // Test 4: Profile isolation
    tests.push(ValidationTest {
        test_name: "Profile Isolation Test".to_string(),
        test_type: "Isolation".to_string(),
        passed: profile.is_temporary || !profile.is_default,
        details: format!("Profile isolation: {}", if profile.is_temporary { "High" } else { "Low" }),
        impact: "Prevents trial data leakage between test sessions".to_string(),
    });

    Ok(tests)
}

fn assess_profile_security(
    profile: &VSCodeProfile,
    persistence_analysis: &ProfilePersistenceAnalysis
) -> SecurityAssessment {
    let mut vulnerabilities = Vec::new();
    let mut strengths = Vec::new();

    // Assess bypass resistance
    let bypass_resistance = persistence_analysis.bypass_resistance_score;

    // Assess isolation effectiveness
    let isolation_effectiveness = if profile.is_temporary { 0.9 } else { 0.3 };

    // Assess persistence strength
    let persistence_strength = if persistence_analysis.cross_session_persistence { 0.8 } else { 0.2 };

    // Identify vulnerabilities
    if !profile.is_temporary {
        vulnerabilities.push("Persistent profile allows trial data to survive restarts".to_string());
    }

    if persistence_analysis.cross_profile_persistence {
        vulnerabilities.push("Trial data may persist across different profiles".to_string());
    }

    if persistence_analysis.server_side_tracking {
        vulnerabilities.push("Server-side tracking bypasses local profile isolation".to_string());
    }

    // Identify strengths
    if persistence_analysis.trial_data_locations.iter().any(|l| l.encrypted) {
        strengths.push("Some trial data is encrypted at rest".to_string());
    }

    if persistence_analysis.trial_data_locations.len() > 2 {
        strengths.push("Multiple storage locations provide redundancy".to_string());
    }

    if bypass_resistance > 0.7 {
        strengths.push("High bypass resistance due to multiple protection layers".to_string());
    }

    let overall_security_score = (bypass_resistance + isolation_effectiveness + persistence_strength) / 3.0;

    SecurityAssessment {
        bypass_resistance,
        isolation_effectiveness,
        persistence_strength,
        overall_security_score,
        vulnerabilities,
        strengths,
    }
}

fn generate_recommendations(
    profile: &VSCodeProfile,
    persistence_analysis: &ProfilePersistenceAnalysis
) -> Vec<String> {
    let mut recommendations = Vec::new();

    if !profile.is_temporary {
        recommendations.push("Consider using a temporary profile for trial testing isolation".to_string());
    }

    if persistence_analysis.cross_session_persistence {
        recommendations.push("Use `--user-data-dir` flag to create isolated testing environments".to_string());
    }

    if persistence_analysis.server_side_tracking {
        recommendations.push("Be aware that server-side validation may persist across profile changes".to_string());
    }

    if persistence_analysis.bypass_resistance_score < 0.5 {
        recommendations.push("Current profile provides limited trial protection - consider security implications".to_string());
    } else {
        recommendations.push("Current profile provides strong trial protection mechanisms".to_string());
    }

    recommendations.push("Always use ethical testing practices and respect software licensing".to_string());

    recommendations
}

fn display_detailed_profile_info(profile: &VSCodeProfile) {
    println!("\n{}", "📋 Detailed Profile Information".bright_cyan().bold());
    println!("Profile Name: {}", profile.profile_name.bright_white());
    println!("Profile Path: {}", profile.profile_path.bright_white());
    println!("Is Default: {}", if profile.is_default { "Yes".green() } else { "No".yellow() });
    println!("Is Temporary: {}", if profile.is_temporary { "Yes".green() } else { "No".red() });
    println!("User Data Dir: {}", profile.user_data_dir.bright_white());
    println!("Extensions Dir: {}", profile.extensions_dir.bright_white());
    println!("Global Storage: {}", profile.global_storage_dir.bright_white());
    println!("Workspace Storage: {}", profile.workspace_storage_dir.bright_white());

    if let Some(created) = profile.created_at {
        println!("Created: {}", created.format("%Y-%m-%d %H:%M:%S UTC").to_string().bright_white());
    }

    if let Some(last_used) = profile.last_used {
        println!("Last Used: {}", last_used.format("%Y-%m-%d %H:%M:%S UTC").to_string().bright_white());
    }
}

async fn validate_trial_persistence(
    mock_mode: bool,
    cross_profile: bool,
    server_side: bool
) -> Result<ProfilePersistenceAnalysis> {
    show_info("🔬 Validating trial data persistence mechanisms...");

    EthicsChecker::verify_educational_operation("validate trial persistence")?;

    let current_profile = if mock_mode {
        create_mock_profile()
    } else {
        detect_real_profile().await?
    };

    let mut analysis = analyze_profile_persistence(&current_profile, mock_mode).await?;

    if cross_profile {
        show_info("Testing cross-profile persistence...");
        analysis.cross_profile_persistence = test_cross_profile_persistence(mock_mode).await?;
    }

    if server_side {
        show_info("Testing server-side persistence...");
        analysis.server_side_tracking = test_server_side_persistence(mock_mode).await?;
    }

    // Recalculate bypass resistance with new data
    analysis.bypass_resistance_score = calculate_enhanced_bypass_resistance(&analysis);

    show_success("✅ Trial persistence validation complete");

    Ok(analysis)
}

async fn test_cross_profile_persistence(_mock_mode: bool) -> Result<bool> {
    // In a real implementation, this would test if trial data persists
    // when switching between different VS Code profiles
    show_info("Simulating profile switch to test data persistence...");

    // Mock result: assume some persistence exists due to system-level storage
    Ok(true)
}

async fn test_server_side_persistence(_mock_mode: bool) -> Result<bool> {
    // In a real implementation, this would test if server-side validation
    // tracks trials independently of local profile changes
    show_info("Testing server-side trial validation...");

    // Mock result: assume server-side tracking exists
    Ok(true)
}

fn calculate_enhanced_bypass_resistance(analysis: &ProfilePersistenceAnalysis) -> f64 {
    let mut base_score = analysis.bypass_resistance_score;

    if analysis.cross_profile_persistence {
        base_score += 0.2;
    }

    if analysis.server_side_tracking {
        base_score += 0.3;
    }

    if analysis.cross_session_persistence {
        base_score += 0.1;
    }

    base_score.min(1.0)
}

async fn analyze_profile_impact(mock_mode: bool, compare: bool) -> Result<Vec<ProfileValidationResult>> {
    show_info("📈 Analyzing profile impact on trial tracking...");

    let mut results = Vec::new();

    // Analyze current profile
    let current_result = detect_current_profile(mock_mode, false).await?;
    results.push(current_result);

    if compare {
        // Generate comparison profiles
        let temp_profile_result = analyze_temporary_profile(mock_mode).await?;
        results.push(temp_profile_result);

        let isolated_profile_result = analyze_isolated_profile(mock_mode).await?;
        results.push(isolated_profile_result);
    }

    show_success(&format!("✅ Profile impact analysis complete: {} profiles analyzed", results.len()));

    Ok(results)
}

async fn analyze_temporary_profile(mock_mode: bool) -> Result<ProfileValidationResult> {
    let mut temp_profile = create_mock_profile();
    temp_profile.profile_name = "Temporary Test Profile".to_string();
    temp_profile.is_temporary = true;
    temp_profile.is_default = false;
    temp_profile.user_data_dir = "/tmp/vscode-temp-profile".to_string();

    let persistence_analysis = analyze_profile_persistence(&temp_profile, mock_mode).await?;
    let validation_tests = run_profile_validation_tests(&temp_profile, mock_mode).await?;
    let security_assessment = assess_profile_security(&temp_profile, &persistence_analysis);
    let recommendations = generate_recommendations(&temp_profile, &persistence_analysis);

    Ok(ProfileValidationResult {
        current_profile: temp_profile,
        persistence_analysis,
        validation_tests,
        security_assessment,
        recommendations,
        educational_notes: vec![
            "Temporary profiles provide better isolation for testing".to_string(),
            "Trial data in temporary profiles may not persist across restarts".to_string(),
        ],
    })
}

async fn analyze_isolated_profile(mock_mode: bool) -> Result<ProfileValidationResult> {
    let mut isolated_profile = create_mock_profile();
    isolated_profile.profile_name = "Isolated Test Profile".to_string();
    isolated_profile.is_default = false;
    isolated_profile.user_data_dir = "/tmp/vscode-isolated-profile".to_string();
    isolated_profile.extensions_dir = "/tmp/vscode-isolated-profile/extensions".to_string();

    let persistence_analysis = analyze_profile_persistence(&isolated_profile, mock_mode).await?;
    let validation_tests = run_profile_validation_tests(&isolated_profile, mock_mode).await?;
    let security_assessment = assess_profile_security(&isolated_profile, &persistence_analysis);
    let recommendations = generate_recommendations(&isolated_profile, &persistence_analysis);

    Ok(ProfileValidationResult {
        current_profile: isolated_profile,
        persistence_analysis,
        validation_tests,
        security_assessment,
        recommendations,
        educational_notes: vec![
            "Isolated profiles use separate directories for complete separation".to_string(),
            "Custom user data directories prevent data contamination".to_string(),
        ],
    })
}

#[derive(Debug, Serialize, Deserialize)]
struct ProfileRecommendations {
    scenario: String,
    recommended_setup: String,
    command_line: String,
    benefits: Vec<String>,
    limitations: Vec<String>,
    security_notes: Vec<String>,
}

async fn generate_profile_recommendations(scenario: &str) -> Result<ProfileRecommendations> {
    show_info(&format!("💡 Generating profile recommendations for scenario: {}", scenario));

    let recommendations = match scenario {
        "trial" => ProfileRecommendations {
            scenario: "Trial Testing".to_string(),
            recommended_setup: "Use default persistent profile for realistic trial experience".to_string(),
            command_line: "code".to_string(),
            benefits: vec![
                "Realistic user experience simulation".to_string(),
                "Proper trial data persistence testing".to_string(),
                "Server-side validation compatibility".to_string(),
            ],
            limitations: vec![
                "Trial data persists between sessions".to_string(),
                "May affect other development work".to_string(),
            ],
            security_notes: vec![
                "This setup matches real user behavior".to_string(),
                "Trial mechanisms work as intended".to_string(),
            ],
        },
        "bypass" => ProfileRecommendations {
            scenario: "Bypass Research".to_string(),
            recommended_setup: "Use isolated temporary profile for ethical research".to_string(),
            command_line: "code --user-data-dir=/tmp/vscode-research".to_string(),
            benefits: vec![
                "Complete isolation from main profile".to_string(),
                "No contamination of production environment".to_string(),
                "Easy cleanup after research".to_string(),
            ],
            limitations: vec![
                "May not reflect real-world persistence".to_string(),
                "Server-side tracking still applies".to_string(),
            ],
            security_notes: vec![
                "Use only for educational purposes".to_string(),
                "Respect software licensing terms".to_string(),
                "Document research methodology".to_string(),
            ],
        },
        "isolation" => ProfileRecommendations {
            scenario: "Complete Isolation".to_string(),
            recommended_setup: "Use temporary profile with network isolation".to_string(),
            command_line: "code --user-data-dir=/tmp/vscode-isolated --disable-extensions".to_string(),
            benefits: vec![
                "Maximum isolation from system".to_string(),
                "No network-based tracking".to_string(),
                "Clean testing environment".to_string(),
            ],
            limitations: vec![
                "Extensions must be manually installed".to_string(),
                "No server-side feature testing".to_string(),
                "Limited real-world applicability".to_string(),
            ],
            security_notes: vec![
                "Suitable for static analysis only".to_string(),
                "Network isolation prevents full functionality".to_string(),
            ],
        },
        _ => ProfileRecommendations {
            scenario: "General Testing".to_string(),
            recommended_setup: "Use default profile with backup".to_string(),
            command_line: "code".to_string(),
            benefits: vec![
                "Balanced approach for general testing".to_string(),
                "Maintains functionality while allowing research".to_string(),
            ],
            limitations: vec![
                "May require manual cleanup".to_string(),
            ],
            security_notes: vec![
                "Always backup important data before testing".to_string(),
            ],
        },
    };

    show_success(&format!("✅ Recommendations generated for {} scenario", scenario));

    Ok(recommendations)
}

async fn simulate_profile_switch(
    from: Option<String>,
    to: Option<String>,
    mock_mode: bool
) -> Result<()> {
    show_info("🔄 Simulating profile switch effects...");

    EthicsChecker::verify_educational_operation("simulate profile switch")?;

    let from_profile = from.unwrap_or_else(|| "Default".to_string());
    let to_profile = to.unwrap_or_else(|| "Temporary".to_string());

    println!("\n{}", "Profile Switch Simulation".bright_cyan().bold());
    println!("From: {} → To: {}", from_profile.bright_white(), to_profile.bright_white());

    if mock_mode {
        println!("\n{}", "Simulated Effects:".bright_yellow());
        println!("• Local trial data: {}", "Isolated".green());
        println!("• Extension settings: {}", "Reset".yellow());
        println!("• Authentication state: {}", "Cleared".red());
        println!("• Server-side tracking: {}", "Persists".red());
        println!("• Hardware fingerprint: {}", "Unchanged".yellow());
    }

    println!("\n{}", "Educational Notes:".bright_blue());
    println!("• Profile switches affect local storage only");
    println!("• Server-side validation may persist across profiles");
    println!("• Hardware fingerprinting remains consistent");
    println!("• Network-based tracking continues");

    show_success("✅ Profile switch simulation complete");

    Ok(())
}

fn explain_profile_concept(topic: &str) {
    println!("\n{}", format!("📚 Educational Explanation: {}", topic).bright_cyan().bold());

    match topic {
        "profiles" => {
            println!("VS Code profiles determine where extension data is stored:");
            println!("• Default Profile: Uses standard system directories");
            println!("• Custom Profile: Uses specified directory with --user-data-dir");
            println!("• Temporary Profile: Often stored in /tmp or similar");
            println!("• Profile isolation affects local storage but not server validation");
        },
        "persistence" => {
            println!("Trial data persistence operates at multiple levels:");
            println!("• Session: Data exists only while VS Code is running");
            println!("• Profile: Data persists in profile-specific directories");
            println!("• System: Data persists in system-wide locations");
            println!("• Server: Data persists on remote servers");
            println!("• Hardware: Fingerprints persist across reinstallations");
        },
        "isolation" => {
            println!("Profile isolation provides varying levels of separation:");
            println!("• Local Isolation: Separate directories for different profiles");
            println!("• Extension Isolation: Different extension installations");
            println!("• Settings Isolation: Separate configuration files");
            println!("• Limited Server Isolation: Server-side tracking may persist");
            println!("• No Hardware Isolation: Fingerprints remain consistent");
        },
        _ => {
            println!("Available topics: profiles, persistence, isolation");
            println!("Use: profile-validator explain <topic>");
        }
    }

    println!("\n{}", "Security Research Ethics:".bright_red());
    println!("• Always use tools for educational purposes only");
    println!("• Respect software licensing and terms of service");
    println!("• Document research methodology and findings");
    println!("• Share knowledge responsibly with the security community");
}
