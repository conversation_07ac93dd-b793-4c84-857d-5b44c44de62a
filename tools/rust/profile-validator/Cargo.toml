[package]
name = "profile-validator"
version = "0.1.0"
edition = "2021"
description = "Educational tool for validating VS Code profile configurations and trial persistence"
authors = ["Security Research Team"]
license = "MIT"

[dependencies]
anyhow = "1.0"
clap = { version = "4.0", features = ["derive"] }
colored = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
dirs = "5.0"
walkdir = "2.0"
regex = "1.0"
sysinfo = "0.30"
shared = { path = "../shared" }

[[bin]]
name = "profile-validator"
path = "src/main.rs"
