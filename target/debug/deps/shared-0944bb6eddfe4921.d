/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/libshared-0944bb6eddfe4921.rmeta: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/libshared-0944bb6eddfe4921.rlib: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/shared-0944bb6eddfe4921.d: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

tools/rust/shared/src/lib.rs:
tools/rust/shared/src/ethics.rs:
tools/rust/shared/src/fingerprint.rs:
tools/rust/shared/src/mock_data.rs:
tools/rust/shared/src/profile.rs:
tools/rust/shared/src/storage.rs:
tools/rust/shared/src/types.rs:
tools/rust/shared/src/utils.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
