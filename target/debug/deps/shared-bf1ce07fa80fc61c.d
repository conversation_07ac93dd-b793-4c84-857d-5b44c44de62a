/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/libshared-bf1ce07fa80fc61c.rmeta: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/libshared-bf1ce07fa80fc61c.rlib: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

/Users/<USER>/Documents/augment-projects/exploit-extension/target/debug/deps/shared-bf1ce07fa80fc61c.d: tools/rust/shared/src/lib.rs tools/rust/shared/src/ethics.rs tools/rust/shared/src/fingerprint.rs tools/rust/shared/src/mock_data.rs tools/rust/shared/src/profile.rs tools/rust/shared/src/storage.rs tools/rust/shared/src/types.rs tools/rust/shared/src/utils.rs

tools/rust/shared/src/lib.rs:
tools/rust/shared/src/ethics.rs:
tools/rust/shared/src/fingerprint.rs:
tools/rust/shared/src/mock_data.rs:
tools/rust/shared/src/profile.rs:
tools/rust/shared/src/storage.rs:
tools/rust/shared/src/types.rs:
tools/rust/shared/src/utils.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
