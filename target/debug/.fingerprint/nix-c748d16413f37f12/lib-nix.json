{"rustc": 5357548097637079788, "features": "[\"default\", \"memoffset\", \"net\", \"socket\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 5347358027863023418, "path": 7489076024816036224, "deps": [[2924422107542798392, "libc", false, 15259917125234422954], [5452785045801004098, "build_script_build", false, 8180576123224728563], [7896293946984509699, "bitflags", false, 16740516057342933970], [10411997081178400487, "cfg_if", false, 10757610167979422610], [14643204177830147187, "memoffset", false, 12780749951354313992]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-c748d16413f37f12/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}