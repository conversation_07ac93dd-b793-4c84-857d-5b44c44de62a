{"$message_type":"diagnostic","message":"unused imports: `PersistenceLevel`, `TrialDataLocation`, and `show_warning`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"tools/rust/profile-validator/src/main.rs","byte_start":506,"byte_end":522,"line_start":13,"line_end":13,"column_start":69,"column_end":85,"is_primary":true,"text":[{"text":"    init, <PERSON><PERSON><PERSON><PERSON>, VSCodeProfile, ProfilePersistenceAnalysis, PersistenceLevel,","highlight_start":69,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tools/rust/profile-validator/src/main.rs","byte_start":528,"byte_end":545,"line_start":14,"line_end":14,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    TrialDataLocation, ToolConfig, OutputFormat, display_results, show_info, ","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tools/rust/profile-validator/src/main.rs","byte_start":606,"byte_end":618,"line_start":15,"line_end":15,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    show_warning, show_success, display_educational_info,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"tools/rust/profile-validator/src/main.rs","byte_start":504,"byte_end":545,"line_start":13,"line_end":14,"column_start":67,"column_end":22,"is_primary":true,"text":[{"text":"    init, EthicsChecker, VSCodeProfile, ProfilePersistenceAnalysis, PersistenceLevel,","highlight_start":67,"highlight_end":86},{"text":"    TrialDataLocation, ToolConfig, OutputFormat, display_results, show_info, ","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"tools/rust/profile-validator/src/main.rs","byte_start":599,"byte_end":618,"line_start":14,"line_end":15,"column_start":76,"column_end":17,"is_primary":true,"text":[{"text":"    TrialDataLocation, ToolConfig, OutputFormat, display_results, show_info, ","highlight_start":76,"highlight_end":78},{"text":"    show_warning, show_success, display_educational_info,","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PersistenceLevel`, `TrialDataLocation`, and `show_warning`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtools/rust/profile-validator/src/main.rs:13:69\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    init, EthicsChecker, VSCodeProfile, ProfilePersistenceAnalysis, PersistenceLevel,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TrialDataLocation, ToolConfig, OutputFormat, display_results, show_info, \u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    show_warning, show_success, display_educational_info,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 1 warning emitted\u001b[0m\n\n"}
