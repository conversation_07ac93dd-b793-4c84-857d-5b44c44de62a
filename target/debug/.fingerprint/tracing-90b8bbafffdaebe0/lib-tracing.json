{"rustc": 5357548097637079788, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 1006155289083248400, "path": 8821022723939007844, "deps": [[1906322745568073236, "pin_project_lite", false, 6988357644946180449], [2967683870285097694, "tracing_attributes", false, 16203863306519485578], [11033263105862272874, "tracing_core", false, 8233791791959678509]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-90b8bbafffdaebe0/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}