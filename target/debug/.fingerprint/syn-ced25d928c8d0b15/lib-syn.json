{"rustc": 5357548097637079788, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 7425689233190052553, "deps": [[1988483478007900009, "unicode_ident", false, 7074285228765218725], [3060637413840920116, "proc_macro2", false, 3680631992195232369], [17990358020177143287, "quote", false, 17306970136787880137]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-ced25d928c8d0b15/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}