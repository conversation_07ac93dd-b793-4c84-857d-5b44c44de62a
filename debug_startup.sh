#!/bin/bash

# Debug startup script for Privacy Fingerprint UI
# This script will start the Tauri app with comprehensive logging

echo "========== DEBUG STARTUP SCRIPT =========="
echo "Timestamp: $(date)"
echo "Working directory: $(pwd)"
echo "=========================================="

# Set environment variables for maximum logging
export RUST_LOG=debug
export RUST_BACKTRACE=1

# Navigate to the UI directory
cd privacy-fingerprint-ui

echo "Starting Tauri app with debug logging..."
echo "All output will be captured to debug_output.log"

# Start the app and capture all output
pnpm tauri dev 2>&1 | tee debug_output.log

echo "========== DEBUG SESSION COMPLETE =========="
