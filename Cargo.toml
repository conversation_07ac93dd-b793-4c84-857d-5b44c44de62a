[workspace]
members = [
    "tools/rust/shared",
    "tools/rust/trial-reset-simulator",
    "tools/rust/fingerprint-analyzer",
    "tools/rust/storage-inspector",
    "tools/rust/network-traffic-simulator",
    "tools/rust/persistence-monitor",
    "tools/rust/profile-validator",
    "tools/rust/privacy-fingerprint-generator"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Security Research Team"]
license = "MIT"
repository = "https://github.com/example/vscode-extension-security-tools"
homepage = "https://github.com/example/vscode-extension-security-tools"
documentation = "https://docs.rs/vscode-extension-security-tools"
keywords = ["vscode", "security", "research", "education", "analysis"]
categories = ["development-tools", "command-line-utilities"]

[workspace.dependencies]
# Core dependencies
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Crypto and hashing
sha2 = "0.10"
uuid = { version = "1.0", features = ["v4", "serde"] }
hex = "0.4"

# File and system operations
dirs = "5.0"
walkdir = "2.0"
tempfile = "3.0"

# Network simulation
reqwest = { version = "0.11", features = ["json"] }
mockito = "1.0"

# Time and date
chrono = { version = "0.4", features = ["serde"] }

# Terminal UI
colored = "2.0"
indicatif = "0.17"
console = "0.15"

# Testing
proptest = "1.0"

# System information
sysinfo = "0.30"
