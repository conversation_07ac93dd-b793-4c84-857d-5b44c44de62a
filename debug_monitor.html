<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Fingerprint UI - Debug Monitor</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .header {
            background-color: #2d2d30;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .log-container {
            background-color: #252526;
            border: 1px solid #3e3e42;
            border-radius: 5px;
            padding: 15px;
            height: 70vh;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #569cd6;
        }
        .log-info {
            color: #4ec9b0;
        }
        .log-warn {
            color: #dcdcaa;
        }
        .log-error {
            color: #f44747;
        }
        .log-debug {
            color: #9cdcfe;
        }
        .highlight {
            background-color: #264f78;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #1177bb;
        }
        .filter-input {
            background-color: #3c3c3c;
            color: #d4d4d4;
            border: 1px solid #3e3e42;
            padding: 6px 10px;
            border-radius: 3px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Privacy Fingerprint UI - Debug Monitor</h1>
        <p>Real-time debugging information for the "Rust Tool Not Available" notification issue</p>
    </div>
    
    <div class="controls">
        <button onclick="clearLogs()">Clear Logs</button>
        <button onclick="toggleAutoScroll()">Toggle Auto-scroll</button>
        <input type="text" class="filter-input" id="filterInput" placeholder="Filter logs..." onkeyup="filterLogs()">
        <button onclick="exportLogs()">Export Logs</button>
    </div>
    
    <div class="log-container" id="logContainer">
        <div class="log-entry log-info">
            <span class="log-timestamp">[Monitor Started]</span> 
            Debug monitor initialized. Waiting for application logs...
        </div>
        <div class="log-entry log-info">
            <span class="log-timestamp">[Instructions]</span> 
            1. Start the Privacy Fingerprint UI application
        </div>
        <div class="log-entry log-info">
            <span class="log-timestamp">[Instructions]</span> 
            2. Open browser developer console (F12) to see frontend logs
        </div>
        <div class="log-entry log-info">
            <span class="log-timestamp">[Instructions]</span> 
            3. Check terminal output for backend logs
        </div>
        <div class="log-entry log-warn">
            <span class="log-timestamp">[Key Points]</span> 
            Look for: <span class="highlight">check_rust_tool_availability</span>, <span class="highlight">NOTIFICATION ADDED</span>, <span class="highlight">Rust Tool Not Available</span>
        </div>
    </div>

    <script>
        let autoScroll = true;
        let allLogs = [];

        function addLog(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp,
                message,
                type,
                id: Date.now() + Math.random()
            };
            
            allLogs.push(logEntry);
            displayLogs();
        }

        function displayLogs() {
            const container = document.getElementById('logContainer');
            const filter = document.getElementById('filterInput').value.toLowerCase();
            
            let filteredLogs = allLogs;
            if (filter) {
                filteredLogs = allLogs.filter(log => 
                    log.message.toLowerCase().includes(filter) ||
                    log.timestamp.toLowerCase().includes(filter)
                );
            }

            container.innerHTML = filteredLogs.map(log => `
                <div class="log-entry log-${log.type}">
                    <span class="log-timestamp">[${log.timestamp}]</span> 
                    ${log.message}
                </div>
            `).join('');

            if (autoScroll) {
                container.scrollTop = container.scrollHeight;
            }
        }

        function clearLogs() {
            allLogs = [];
            displayLogs();
            addLog('Logs cleared', 'info');
        }

        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            addLog(`Auto-scroll ${autoScroll ? 'enabled' : 'disabled'}`, 'info');
        }

        function filterLogs() {
            displayLogs();
        }

        function exportLogs() {
            const logText = allLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug_logs_${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Simulate receiving logs (in real implementation, this would come from the app)
        function simulateIncomingLogs() {
            // This is just for demonstration - in practice, logs would come from the actual app
            setTimeout(() => {
                addLog('Application startup detected', 'info');
            }, 2000);
            
            setTimeout(() => {
                addLog('Frontend initialization started', 'debug');
            }, 3000);
            
            setTimeout(() => {
                addLog('Backend Tauri commands registered', 'debug');
            }, 4000);
        }

        // Start simulation (remove this in production)
        simulateIncomingLogs();

        // Instructions for manual log entry
        console.log('Debug Monitor Ready!');
        console.log('To manually add logs, use: addLog("Your message", "info|warn|error|debug")');
        
        // Make addLog available globally for manual testing
        window.addLog = addLog;
    </script>
</body>
</html>
