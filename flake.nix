{
  description = "VS Code Extension Security Research with Fingerprint Isolation";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
    rust-overlay = {
      url = "github:oxalica/rust-overlay";
      inputs.nixpkgs.follows = "nixpkgs";
    };
  };

  outputs = { self, nixpkgs, flake-utils, rust-overlay }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        overlays = [ (import rust-overlay) ];
        pkgs = import nixpkgs {
          inherit system overlays;
          config = {
            allowUnfree = true;
          };
        };

        # Rust toolchain
        rustToolchain = pkgs.rust-bin.stable.latest.default.override {
          extensions = [ "rust-src" "rust-analyzer" ];
        };

        # VS Code for testing (extensions can be installed manually in isolated environments)
        vscode = pkgs.vscode;

        # Build script for fingerprint generator
        buildFingerprintGenerator = pkgs.writeShellScriptBin "build-fingerprint-generator" ''
          echo "🔧 Building privacy fingerprint generator..."
          ${rustToolchain}/bin/cargo build --release -p privacy-fingerprint-generator
          echo "✅ Build complete"
        '';

        # Script to generate new fingerprint
        generateFingerprint = pkgs.writeShellScriptBin "generate-fingerprint" ''
          set -e
          
          FINGERPRINT_DIR="$1"
          PRIVACY_LEVEL="''${2:-high}"
          
          if [ -z "$FINGERPRINT_DIR" ]; then
            echo "❌ Error: Fingerprint directory required"
            echo "Usage: generate-fingerprint <dir> [privacy_level]"
            exit 1
          fi
          
          mkdir -p "$FINGERPRINT_DIR"
          
          echo "🔐 Generating new privacy fingerprint..."
          ${rustToolchain}/bin/cargo run --release -p privacy-fingerprint-generator -- \
            generate \
            --real-system \
            --save-path "$FINGERPRINT_DIR/fingerprint.json" \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation
          
          echo "✅ Fingerprint generated: $FINGERPRINT_DIR/fingerprint.json"
        '';

        # Script to create isolated VS Code environment
        createIsolatedVSCode = pkgs.writeShellScriptBin "create-isolated-vscode" ''
          set -e
          
          PROFILE_NAME="''${1:-$(date +%Y%m%d_%H%M%S)}"
          FINGERPRINT_PATH="$2"
          WORKSPACE_PATH="''${3:-$(pwd)}"
          
          # Create isolated directories
          PROFILE_DIR="/tmp/vscode-isolated-$PROFILE_NAME"
          USER_DATA_DIR="$PROFILE_DIR/user-data"
          EXTENSIONS_DIR="$PROFILE_DIR/extensions"
          WORKSPACE_STORAGE_DIR="$PROFILE_DIR/workspace-storage"
          
          echo "🏗️  Creating isolated VS Code environment..."
          echo "   Profile: $PROFILE_NAME"
          echo "   Directory: $PROFILE_DIR"
          
          mkdir -p "$USER_DATA_DIR"
          mkdir -p "$EXTENSIONS_DIR"
          mkdir -p "$WORKSPACE_STORAGE_DIR"
          
          # Copy fingerprint if provided
          if [ -n "$FINGERPRINT_PATH" ] && [ -f "$FINGERPRINT_PATH" ]; then
            echo "🔐 Installing fingerprint..."
            mkdir -p "$USER_DATA_DIR/User/globalStorage"
            cp "$FINGERPRINT_PATH" "$USER_DATA_DIR/User/globalStorage/fingerprint.json"
          fi
          
          # Create VS Code settings with fingerprint integration
          mkdir -p "$USER_DATA_DIR/User"
          cat > "$USER_DATA_DIR/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.settings.enableNaturalLanguageSearch": false,
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$PROFILE_NAME",
  "privacy.fingerprintPath": "$USER_DATA_DIR/User/globalStorage/fingerprint.json"
}
EOF
          
          echo "✅ Isolated environment created: $PROFILE_DIR"
          echo "$PROFILE_DIR"
        '';

      in
      {
        devShells.default = pkgs.mkShell {
          buildInputs = with pkgs; [
            # Rust development
            rustToolchain
            pkg-config
            openssl
            
            # Node.js for frontend
            nodejs_20
            nodePackages.pnpm
            
            # VS Code
            vscode
            
            # System tools
            jq
            curl
            git
            
            # Custom scripts
            buildFingerprintGenerator
            generateFingerprint
            createIsolatedVSCode
          ];

          shellHook = ''
            echo "🚀 VS Code Extension Security Research Environment"
            echo "=================================================="
            echo ""
            echo "Available commands:"
            echo "  build-fingerprint-generator  - Build the Rust fingerprint generator"
            echo "  generate-fingerprint <dir>   - Generate new privacy fingerprint"
            echo "  create-isolated-vscode       - Create isolated VS Code environment"
            echo ""
            echo "Quick start:"
            echo "  ./scripts/nix-vscode-runner.sh --new-fingerprint"
            echo ""
            
            # Build tools on first run
            if [ ! -f "target/release/privacy-fingerprint-generator" ]; then
              echo "🔧 Building tools for first time..."
              build-fingerprint-generator
            fi
          '';
        };

        packages = {
          default = buildFingerprintGenerator;
          fingerprint-generator = buildFingerprintGenerator;
          vscode-isolated = createIsolatedVSCode;
        };

        apps = {
          default = flake-utils.lib.mkApp {
            drv = buildFingerprintGenerator;
          };
        };
      });
}
