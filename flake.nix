{
  description = "Simple VS Code Fingerprint Isolation";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
  };

  outputs = { self, nixpkgs }:
    let
      system = "aarch64-darwin"; # Adjust for your system
      pkgs = import nixpkgs {
        inherit system;
        config.allowUnfree = true;
      };
    in {
      devShells.${system}.default = pkgs.mkShell {
        buildInputs = with pkgs; [
          # Just the essentials
          rustc
          cargo
          jq
          curl
        ];

        shellHook = ''
          echo "🚀 Simple VS Code Fingerprint Isolation"
          echo "======================================="
          echo ""
          echo "Run: ./scripts/simple-vscode-runner.sh --new-fingerprint"
          echo ""
        '';
      };

    };
}
