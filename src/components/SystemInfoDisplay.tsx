import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  Monitor, 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Clock, 
  Info,
  RefreshCw,
  Server,
  Zap
} from 'lucide-react';
import type { SystemInfo } from '@types/index';

const SystemInfoDisplay: React.FC = () => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSystemInfo = async () => {
    setLoading(true);
    setError(null);
    try {
      const info = await invoke<SystemInfo>('get_system_info');
      setSystemInfo(info);
    } catch (err) {
      setError(`Failed to fetch system information: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemInfo();
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const formatFrequency = (mhz: number): string => {
    if (mhz >= 1000) {
      return `${(mhz / 1000).toFixed(2)} GHz`;
    }
    return `${mhz} MHz`;
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="flex items-center justify-center py-8">
            <div className="w-8 h-8 border-2 border-monokai-green border-t-transparent rounded-full animate-spin"></div>
            <span className="ml-3 text-monokai-text-secondary">Loading system information...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-body">
          <div className="text-center py-8">
            <div className="text-monokai-red mb-4">
              <Info className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-monokai-text-primary mb-2">
              Error Loading System Information
            </h3>
            <p className="text-monokai-text-muted mb-4">{error}</p>
            <button
              onClick={fetchSystemInfo}
              className="btn btn-primary"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!systemInfo) return null;

  const memoryUsagePercent = ((systemInfo.memory_total - systemInfo.memory_available) / systemInfo.memory_total) * 100;
  const diskUsagePercent = ((systemInfo.disk_total - systemInfo.disk_available) / systemInfo.disk_total) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-monokai-text-primary mb-2">
            System Information
          </h2>
          <p className="text-monokai-text-secondary">
            Real-time hardware and system details for fingerprint generation
          </p>
        </div>
        <button
          onClick={fetchSystemInfo}
          className="btn btn-ghost btn-sm flex items-center space-x-2"
          title="Refresh system information"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Operating System */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Monitor className="w-5 h-5 text-monokai-blue" />
              <h3 className="font-semibold text-monokai-text-primary">Operating System</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">OS:</span>
              <span className="text-monokai-text-primary font-medium">{systemInfo.os}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Version:</span>
              <span className="text-monokai-text-primary font-medium text-sm">{systemInfo.os_version}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Architecture:</span>
              <span className="text-monokai-text-primary font-medium">{systemInfo.arch}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Platform:</span>
              <span className="text-monokai-text-primary font-medium">{systemInfo.platform}</span>
            </div>
          </div>
        </div>

        {/* CPU Information */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Cpu className="w-5 h-5 text-monokai-green" />
              <h3 className="font-semibold text-monokai-text-primary">Processor</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Brand:</span>
              <span className="text-monokai-text-primary font-medium text-sm">{systemInfo.cpu_brand}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Cores:</span>
              <span className="text-monokai-text-primary font-medium">{systemInfo.cpu_count}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Frequency:</span>
              <span className="text-monokai-text-primary font-medium">{formatFrequency(systemInfo.cpu_frequency)}</span>
            </div>
          </div>
        </div>

        {/* Memory Information */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <MemoryStick className="w-5 h-5 text-monokai-purple" />
              <h3 className="font-semibold text-monokai-text-primary">Memory</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Total:</span>
              <span className="text-monokai-text-primary font-medium">{formatBytes(systemInfo.memory_total)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Available:</span>
              <span className="text-monokai-text-primary font-medium">{formatBytes(systemInfo.memory_available)}</span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-monokai-text-muted">Usage:</span>
                <span className="text-monokai-text-primary">{memoryUsagePercent.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-monokai-bg-tertiary rounded-full h-2">
                <div 
                  className="bg-monokai-purple h-2 rounded-full transition-all duration-300"
                  style={{ width: `${memoryUsagePercent}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Storage Information */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <HardDrive className="w-5 h-5 text-monokai-yellow" />
              <h3 className="font-semibold text-monokai-text-primary">Storage</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Total:</span>
              <span className="text-monokai-text-primary font-medium">{formatBytes(systemInfo.disk_total)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Available:</span>
              <span className="text-monokai-text-primary font-medium">{formatBytes(systemInfo.disk_available)}</span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-monokai-text-muted">Usage:</span>
                <span className="text-monokai-text-primary">{diskUsagePercent.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-monokai-bg-tertiary rounded-full h-2">
                <div 
                  className="bg-monokai-yellow h-2 rounded-full transition-all duration-300"
                  style={{ width: `${diskUsagePercent}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-monokai-red" />
              <h3 className="font-semibold text-monokai-text-primary">System Status</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Uptime:</span>
              <span className="text-monokai-text-primary font-medium">{formatUptime(systemInfo.uptime)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-monokai-text-muted">Boot Time:</span>
              <span className="text-monokai-text-primary font-medium text-sm">
                {new Date(systemInfo.boot_time * 1000).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Tool Status */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <Server className="w-5 h-5 text-monokai-green" />
              <h3 className="font-semibold text-monokai-text-primary">Tool Status</h3>
            </div>
          </div>
          <div className="card-body space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-monokai-text-muted">Rust Tool:</span>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${systemInfo.rust_tool_available ? 'bg-monokai-green' : 'bg-monokai-red'}`}></div>
                <span className={`text-sm font-medium ${systemInfo.rust_tool_available ? 'text-monokai-green' : 'text-monokai-red'}`}>
                  {systemInfo.rust_tool_available ? 'Available' : 'Unavailable'}
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-monokai-text-muted">Mode:</span>
              <span className="text-monokai-text-primary font-medium">
                {systemInfo.rust_tool_available ? 'Real System' : 'Mock Data'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemInfoDisplay;
