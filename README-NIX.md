# Nix-based VS Code Fingerprint Isolation

This project now includes Nix-based scripts that can run VS Code with different fingerprints on each execution, providing complete isolation for testing extension behavior and trial prevention mechanisms.

## 🚀 Quick Start

### Prerequisites

1. **Install Nix** (if not already installed):
   ```bash
   # On macOS/Linux
   curl -L https://nixos.org/nix/install | sh
   
   # Enable flakes (recommended)
   echo "experimental-features = nix-command flakes" >> ~/.config/nix/nix.conf
   ```

2. **Enter the Nix environment**:
   ```bash
   # Using flakes (recommended)
   nix develop
   
   # Or using legacy shell.nix
   nix-shell
   ```

### Basic Usage

```bash
# Run VS Code with a new fingerprint
./scripts/nix-vscode-runner.sh --new-fingerprint

# Run with maximum privacy
./scripts/nix-vscode-runner.sh --new-fingerprint --privacy-level maximum

# Open specific workspace with new fingerprint
./scripts/nix-vscode-runner.sh --new-fingerprint /path/to/workspace

# List available fingerprints
./scripts/nix-vscode-runner.sh --list-fingerprints

# Use existing fingerprint
./scripts/nix-vscode-runner.sh --fingerprint-id fp_20241201_123456
```

## 🔧 Features

### Complete Isolation
- **Unique device fingerprints** generated for each session
- **Isolated user data directories** (no shared settings/extensions)
- **Separate extension storage** (no cross-contamination)
- **Privacy-protected system signatures** (real or mock data)
- **Rotatable fingerprint components** (automatic or manual)

### Privacy Levels
- **Low**: Basic fingerprinting with minimal privacy protection
- **Medium**: Balanced approach with moderate anonymization
- **High**: Strong privacy protection with data minimization
- **Maximum**: Extreme privacy with maximum anonymization

### Fingerprint Components
Each generated fingerprint includes:
- **Hardware signature**: CPU, memory, architecture (anonymized)
- **System signature**: OS family, version class, timezone class
- **VS Code signature**: Version class, machine ID hash
- **Network signature**: MAC address signature (hashed)
- **Privacy metadata**: Anonymization level, tracking protection

## 📋 Available Scripts

### Main Runner Script
```bash
./scripts/nix-vscode-runner.sh [OPTIONS] [WORKSPACE_PATH]

Options:
  --new-fingerprint, -n    Generate new fingerprint for this session
  --fingerprint-id ID      Use specific fingerprint by ID
  --privacy-level LEVEL    Set privacy level (low, medium, high, maximum)
  --profile-name NAME      Use custom profile name
  --list-fingerprints, -l  List available fingerprints
  --clean, -c              Clean up old profiles and fingerprints
  --verbose, -v            Enable verbose output
  --dry-run                Show what would be done without executing
```

### Fingerprint Manager
```bash
./scripts/fingerprint-manager.sh COMMAND [OPTIONS]

Commands:
  list                     List all available fingerprints
  show ID                  Show detailed fingerprint information
  generate [OPTIONS]       Generate new fingerprint
  rotate ID                Rotate existing fingerprint
  delete ID                Delete fingerprint
  validate ID              Validate fingerprint integrity
```

### Package.json Scripts
```bash
# Quick commands via npm/pnpm
npm run nix:vscode                    # Run with new fingerprint
npm run nix:vscode:new-fp            # Same as above
npm run nix:vscode:clean             # Clean up old data
```

## 🔍 Example Workflows

### Testing Extension Trial Prevention
```bash
# Generate fingerprint and test extension
./scripts/nix-vscode-runner.sh --new-fingerprint --privacy-level high

# Install your extension in the isolated VS Code
# Test trial functionality

# Generate different fingerprint for bypass testing
./scripts/nix-vscode-runner.sh --new-fingerprint --privacy-level maximum

# Test if extension detects different device
```

### Comparing Fingerprint Effectiveness
```bash
# Generate multiple fingerprints
./scripts/fingerprint-manager.sh generate --privacy-level low --save-as fp_low
./scripts/fingerprint-manager.sh generate --privacy-level high --save-as fp_high
./scripts/fingerprint-manager.sh generate --privacy-level maximum --save-as fp_max

# Compare them
./scripts/fingerprint-manager.sh show fp_low
./scripts/fingerprint-manager.sh show fp_high
./scripts/fingerprint-manager.sh show fp_max

# Test with each
./scripts/nix-vscode-runner.sh --fingerprint-id fp_low
./scripts/nix-vscode-runner.sh --fingerprint-id fp_high
./scripts/nix-vscode-runner.sh --fingerprint-id fp_max
```

### Development Workflow
```bash
# Enter Nix environment
nix develop

# Build tools
cargo build --release

# Generate test fingerprint
./scripts/fingerprint-manager.sh generate --real-system --enable-rotation

# Run VS Code with fingerprint
./scripts/nix-vscode-runner.sh --new-fingerprint ./my-extension-project

# Clean up when done
./scripts/nix-vscode-runner.sh --clean
```

## 🛡️ Privacy & Security

### Data Protection
- All fingerprints use **cryptographic hashing** for sensitive data
- **No personally identifiable information** is stored in plain text
- **Configurable anonymization levels** based on privacy requirements
- **Automatic cleanup** of old profiles and fingerprints

### Isolation Guarantees
- **Complete filesystem isolation** between VS Code instances
- **No shared state** between different fingerprint sessions
- **Separate extension storage** prevents cross-contamination
- **Isolated network signatures** for each session

### Educational Use Only
⚠️ **Important**: These tools are designed for:
- **Educational purposes** and security research
- **Understanding fingerprinting techniques**
- **Testing extension security mechanisms**
- **Improving privacy protection**

**Do NOT use** to bypass commercial software licensing or violate terms of service.

## 🔧 Technical Details

### Nix Environment
The `flake.nix` provides:
- **Rust toolchain** with required dependencies
- **VS Code with extensions** for testing
- **Node.js and pnpm** for frontend development
- **Custom scripts** for fingerprint management

### File Structure
```
.fingerprints/           # Generated fingerprints
├── fp_20241201_123456.json
├── fp_20241201_124567.json
└── ...

/tmp/vscode-fingerprint-profiles/  # Isolated VS Code profiles
├── vscode-fp-fp_20241201_123456/
│   ├── user-data/
│   ├── extensions/
│   └── workspace-storage/
└── ...
```

### Integration with Existing Tools
The Nix scripts integrate seamlessly with the existing Rust tools:
- Uses `privacy-fingerprint-generator` for fingerprint creation
- Leverages `profile-validator` for VS Code profile management
- Compatible with existing fingerprint analysis tools

## 🐛 Troubleshooting

### Common Issues

1. **Nix not found**:
   ```bash
   # Install Nix first
   curl -L https://nixos.org/nix/install | sh
   ```

2. **VS Code fails to start**:
   ```bash
   # Check if VS Code is available in Nix environment
   which code
   
   # Try with verbose output
   ./scripts/nix-vscode-runner.sh --new-fingerprint --verbose
   ```

3. **Permission errors**:
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh
   ```

4. **Build failures**:
   ```bash
   # Clean and rebuild
   cargo clean
   cargo build --release
   ```

### Debug Mode
```bash
# Run with verbose output and dry-run
./scripts/nix-vscode-runner.sh --new-fingerprint --verbose --dry-run

# Check generated fingerprint
./scripts/fingerprint-manager.sh show fp_XXXXXX_XXXXXX
```

## 📚 Further Reading

- [Privacy Fingerprint Generator Documentation](tools/rust/privacy-fingerprint-generator/README.md)
- [VS Code Profile Management](tools/rust/shared/src/profile.rs)
- [Nix Flakes Documentation](https://nixos.wiki/wiki/Flakes)
- [Project Ethics Guidelines](ETHICS.md)
