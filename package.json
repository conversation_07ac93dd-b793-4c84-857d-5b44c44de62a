{"name": "exploit-extension", "version": "1.0.0", "description": "VS Code Extension Security Research with Nix-based Fingerprint Isolation", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "nix:vscode": "./scripts/nix-vscode-runner.sh", "nix:vscode:new-fp": "./scripts/nix-vscode-runner.sh --new-fingerprint", "nix:vscode:clean": "./scripts/nix-vscode-runner.sh --clean"}, "private": true}