{"name": "exploit-extension", "version": "1.0.0", "description": "VS Code Extension Security Research with Nix-based Fingerprint Isolation", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "vscode": "./scripts/simple-vscode-runner.sh --new-fingerprint", "vscode:new": "./scripts/simple-vscode-runner.sh --new-fingerprint", "vscode:list": "./scripts/simple-vscode-runner.sh --list", "vscode:clean": "./scripts/simple-vscode-runner.sh --clean"}, "private": true}