{"id": "fp_20250606_110910", "version": "1.0.0", "created_at": "2025-06-06T04:09:10Z", "privacy_level": "high", "rotation_enabled": true, "device_id": "04fb88874204a5e2038b4a9b4d69f0c7", "hardware_signature": {"cpu_signature": "mock-cpu-bbd020dc", "memory_class": "8-16GB", "architecture": "arm64", "hardware_uuid_hash": "a82407097fa4ec9f299c76bb409d9706", "performance_class": "high"}, "system_signature": {"os_family": "<PERSON>", "os_version_class": "recent", "timezone_class": "+07", "locale_class": "en-US"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "136a1d17a5b0694f1df88517e1df52ac", "session_id_hash": "690875aca3bdd9e3af8e9c80d73fadb7"}, "network_signature": {"mac_signature": "d274d53fef7816e96c140fb0", "network_class": "ethernet"}, "privacy_metadata": {"anonymization_level": "high", "data_minimization_applied": true, "tracking_protection_enabled": true}, "fingerprint_hash": "232a8991124512c187816efc910607adfd6c878d9a5beb2c88b7ea1104dffa83"}