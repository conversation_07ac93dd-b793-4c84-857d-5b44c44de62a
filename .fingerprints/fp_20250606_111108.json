{"id": "fp_20250606_111108", "version": "1.0.0", "created_at": "2025-06-06T04:11:08Z", "privacy_level": "high", "rotation_enabled": true, "device_id": "0c314d012796debe914089b67a5f3f22", "hardware_signature": {"cpu_signature": "mock-cpu-508ab589", "memory_class": "8-16GB", "architecture": "arm64", "hardware_uuid_hash": "72735bde3508bd989d6fe738c4afb661", "performance_class": "high"}, "system_signature": {"os_family": "<PERSON>", "os_version_class": "recent", "timezone_class": "+07", "locale_class": "en-US"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "ea0bbc533e2db94cabda0b6174c9a46d", "session_id_hash": "ea18554351b3daa787436bca5b21a248"}, "network_signature": {"mac_signature": "8de2391408045743fd7bff23", "network_class": "ethernet"}, "privacy_metadata": {"anonymization_level": "high", "data_minimization_applied": true, "tracking_protection_enabled": true}, "fingerprint_hash": "699a29bba990e6c3c937f9c758ae867312ea36c2edc5d99cba4008205412326d"}