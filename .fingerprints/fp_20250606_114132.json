{"id": "fp_20250606_114132", "version": "1.0.0", "created_at": "2025-06-06T04:41:33Z", "privacy_level": "high", "rotation_enabled": true, "device_id": "f327c1ed3defed2d3156de2cc1898ec7", "hardware_signature": {"cpu_signature": "mock-cpu-80c95fc1", "memory_class": "8-16GB", "architecture": "arm64", "hardware_uuid_hash": "cdf2820145a4a81a478ff5af5f2e75a3", "performance_class": "high"}, "system_signature": {"os_family": "<PERSON>", "os_version_class": "recent", "timezone_class": "+07", "locale_class": "en-US"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "ea0801d5952c9f8e32c0f5db5a429ba9", "session_id_hash": "78f0cb4bc49fe0d01294747c98fda2c2"}, "network_signature": {"mac_signature": "7adf3fdae5d5522fef0aa182", "network_class": "ethernet"}, "privacy_metadata": {"anonymization_level": "high", "data_minimization_applied": true, "tracking_protection_enabled": true}, "fingerprint_hash": "c1c1109035dc76d635fbdab84d8a9817ee0ef1deaf3fbc260a1fbe274cd007c8"}