{"id": "fp_20250606_114258", "version": "1.0.0", "created_at": "2025-06-06T04:42:58Z", "privacy_level": "high", "rotation_enabled": true, "device_id": "a29663e735fc127a819f1b53f160c9d7", "hardware_signature": {"cpu_signature": "mock-cpu-e3f5115f", "memory_class": "8-16GB", "architecture": "arm64", "hardware_uuid_hash": "f98f71e9201a9526fd5d417b7aabd8d0", "performance_class": "high"}, "system_signature": {"os_family": "<PERSON>", "os_version_class": "recent", "timezone_class": "+07", "locale_class": "en-US"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "51182cc62138daf4dbe381abb892beb9", "session_id_hash": "0008f252005c290799d1211670baa92f"}, "network_signature": {"mac_signature": "5178babae814c1645332f071", "network_class": "ethernet"}, "privacy_metadata": {"anonymization_level": "high", "data_minimization_applied": true, "tracking_protection_enabled": true}, "fingerprint_hash": "eef118f0e10994e89157ca26431b42c8d35b7ce1197293f892d4083e916d6bf4"}